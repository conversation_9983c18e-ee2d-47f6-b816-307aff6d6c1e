# 使用官方OpenJDK镜像（指定具体版本）
FROM registry.cn-hangzhou.aliyuncs.com/library/openjdk:17-jdk-slim

# 方案1：使用华为云镜像（推荐）
FROM swr.cn-east-2.myhuaweicloud.com/library/openjdk:17-jdk

# 方案2：使用腾讯云镜像
FROM ccr.ccs.tencentyun.com/mirrorhub/openjdk:17-jdk

# 方案3：使用微软官方镜像
FROM mcr.microsoft.com/openjdk/jdk:17-ubuntu


# 设置工作目录
WORKDIR /app

# 添加维护者信息
LABEL maintainer="WhiskerGuard Team"

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 复制已构建好的jar文件
COPY target/*.jar app.jar

# 设置环境变量
ENV SPRING_PROFILES_ACTIVE=prod \
    JAVA_OPTS="-Xmx512m -Xms256m"

#skywalking
COPY skywalking/skywalking-agent.tgz /tmp/
RUN mkdir -p /skywalking/agent && \
tar -zxvf /tmp/skywalking-agent.tgz -C /skywalking/agent  --strip-components=1 && \
rm -f /tmp/skywalking-agent.tgz \

# Expose the application port
EXPOSE 8080

# Run the application
ENTRYPOINT ["sh", "-c", \
    "java ${JAVA_OPTS} \
    -javaagent:/skywalking/agent/skywalking-agent.jar \
    -Dskywalking.agent.service_name=${SPRING_PROFILES_ACTIVE}_gatewayservice \
    -Dskywalking.collector.backend_service=www.skywalking.mbbhg.com \
    -jar app.jar"]
