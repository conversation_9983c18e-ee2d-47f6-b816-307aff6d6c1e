{"generator-jhipster": {"applicationType": "gateway", "authenticationType": "jwt", "baseName": "gatewayService", "buildTool": "maven", "cacheProvider": null, "clientFramework": "no", "clientTestFrameworks": null, "clientTheme": null, "creationTimestamp": 1746678775137, "databaseType": "sql", "devDatabaseType": "mysql", "enableHibernateCache": null, "enableTranslation": true, "entities": [], "feignClient": null, "jhipsterVersion": "8.10.0", "jwtSecretKey": "MWQyZThlMWEwYjcyYTczYWM1ZWU3ZjJmM2YyMzViNzRlNDkzZjJhNDRlOTg2NzhiZjE4NTEzMDk0M2EwY2FiNjI4NzdiMGE0YmIwMjc1MjRjZDk1MzhiYjJiNTkxOWQzNjBlM2NlMDZiZDliNDg3OGQzMjlkNDE5ZWM3OTIxNTg=", "languages": ["zh-cn"], "microfrontend": null, "microfrontends": [], "nativeLanguage": "zh-cn", "packageName": "com.whiskerguard.gateway", "prodDatabaseType": "mysql", "reactive": true, "serverPort": "8080", "serviceDiscoveryType": "consul", "skipClient": true, "skipUserManagement": true, "syncUserWithIdp": null, "testFrameworks": [], "withAdminUi": null}}