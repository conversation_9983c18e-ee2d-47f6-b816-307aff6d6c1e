# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: gatewayservice
services:
  mysql:
    image: mysql:9.2.0
    volumes:
      - ./config/mysql:/etc/mysql/conf.d
    environment:
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes
      - MYSQL_DATABASE=gatewayservice
    # If you want to expose these ports outside your dev PC,
    # remove the "127.0.0.1:" prefix
    ports:
      - 127.0.0.1:3307:3306
    command: mysqld --lower_case_table_names=1 --skip-mysqlx --character_set_server=utf8mb4 --explicit_defaults_for_timestamp
    healthcheck:
      test: ['CMD-SHELL', 'mysql -e "SHOW DATABASES;" && sleep 5']
      interval: 5s
      timeout: 10s
      retries: 10
