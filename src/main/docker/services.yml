# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: gatewayservice
services:
  mysql:
    extends:
      file: ./mysql.yml
      service: mysql
  consul:
    extends:
      file: ./consul.yml
      service: consul
  consul-config-loader:
    extends:
      file: ./consul.yml
      service: consul-config-loader
  redis:
    image: redis:7.4.2
    ports:
      - '6379:6379'
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ['CMD', 'redis-cli', 'ping']
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  redis-data:
