# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: gatewayservice
services:
  app:
    image: gatewayservice
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - MANAGEMENT_PROMETHEUS_METRICS_EXPORT_ENABLED=true
      - SPRING_CLOUD_CONSUL_HOST=consul
      - SPRING_CLOUD_CONSUL_PORT=8500
      - SPRING_R2DBC_URL=r2dbc:mysql://mysql:3306/gatewayservice?useUnicode=true&characterEncoding=utf8&useSSL=false&useLegacyDatetimeCode=false&createDatabaseIfNotExist=true
      - SPRING_LIQUIBASE_URL=****************************************************************************************************************************************************
    ports:
      - 127.0.0.1:8080:8080
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/management/health
      interval: 5s
      timeout: 5s
      retries: 40
    depends_on:
      mysql:
        condition: service_healthy
  mysql:
    extends:
      file: ./mysql.yml
      service: mysql
  consul:
    extends:
      file: ./consul.yml
      service: consul
  consul-config-loader:
    extends:
      file: ./consul.yml
      service: consul-config-loader
