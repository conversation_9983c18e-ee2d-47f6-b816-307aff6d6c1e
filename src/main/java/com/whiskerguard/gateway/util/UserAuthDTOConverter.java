package com.whiskerguard.gateway.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.gateway.dto.UserAuthDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * UserAuthDTO转换工具类
 * 用于处理包含类型信息的特殊JSON格式转换
 */
@Component
public class UserAuthDTOConverter {

    private static final Logger LOG = LoggerFactory.getLogger(UserAuthDTOConverter.class);

    private final ObjectMapper objectMapper;

    public UserAuthDTOConverter(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }

    /**
     * 将包含类型信息的JSON字符串转换为UserAuthDTO对象
     *
     *
     * @param jsonString 包含类型信息的JSON字符串
     * @return UserAuthDTO对象
     * @throws JsonProcessingException JSON解析异常
     */
    public UserAuthDTO fromTypedJson(String jsonString) throws JsonProcessingException {
        LOG.debug("Converting typed JSON to UserAuthDTO: {}", jsonString);

        JsonNode rootNode = objectMapper.readTree(jsonString);
        UserAuthDTO userAuthDTO = new UserAuthDTO();

        // 处理 @class 字段（忽略，仅用于类型识别）
        if (rootNode.has("@class")) {
            LOG.debug("Found @class field: {}", rootNode.get("@class").asText());
        }

        // 处理 active 字段
        if (rootNode.has("active")) {
            userAuthDTO.setActive(rootNode.get("active").asBoolean());
        }

        // 处理 id 字段（可能是数组格式）
        if (rootNode.has("id")) {
            JsonNode idNode = rootNode.get("id");
            Long id = extractLongValue(idNode);
            userAuthDTO.setId(id);
        }

        // 处理 password 字段
        if (rootNode.has("password")) {
            userAuthDTO.setPassword(rootNode.get("password").asText());
        }

        // 处理 salt 字段
        if (rootNode.has("salt")) {
            userAuthDTO.setSalt(rootNode.get("salt").asText());
        }

        // 处理 tenantId 字段（可能是数组格式）
        if (rootNode.has("tenantId")) {
            JsonNode tenantIdNode = rootNode.get("tenantId");
            Long tenantId = extractLongValue(tenantIdNode);
            userAuthDTO.setTenantId(tenantId);
        }

        // 处理 username 字段
        if (rootNode.has("username")) {
            userAuthDTO.setUsername(rootNode.get("username").asText());
        }

        LOG.debug("Successfully converted to UserAuthDTO: id={}, username={}, active={}, tenantId={}",
                 userAuthDTO.getId(), userAuthDTO.getUsername(), userAuthDTO.isActive(), userAuthDTO.getTenantId());

        return userAuthDTO;
    }

    /**
     * 将UserAuthDTO对象转换为标准JSON字符串
     *
     * @param userAuthDTO UserAuthDTO对象
     * @return 标准JSON字符串
     * @throws JsonProcessingException JSON序列化异常
     */
    public String toJson(UserAuthDTO userAuthDTO) throws JsonProcessingException {
        LOG.debug("Converting UserAuthDTO to JSON: {}", userAuthDTO.getUsername());
        return objectMapper.writeValueAsString(userAuthDTO);
    }

    /**
     * 将UserAuthDTO对象转换为包含类型信息的JSON字符串
     *
     * @param userAuthDTO UserAuthDTO对象
     * @return 包含类型信息的JSON字符串
     * @throws JsonProcessingException JSON序列化异常
     */
    public String toTypedJson(UserAuthDTO userAuthDTO) throws JsonProcessingException {
        LOG.debug("Converting UserAuthDTO to typed JSON: {}", userAuthDTO.getUsername());

        StringBuilder jsonBuilder = new StringBuilder();
        jsonBuilder.append("{");
        jsonBuilder.append("\"@class\":\"com.whiskerguard.auth.service.dto.UserAuthDTO\"");

        if (userAuthDTO.getId() != null) {
            jsonBuilder.append(",\"id\":[\"java.lang.Long\",").append(userAuthDTO.getId()).append("]");
        }

        if (userAuthDTO.getUsername() != null) {
            jsonBuilder.append(",\"username\":\"").append(escapeJson(userAuthDTO.getUsername())).append("\"");
        }

        if (userAuthDTO.getPassword() != null) {
            jsonBuilder.append(",\"password\":\"").append(escapeJson(userAuthDTO.getPassword())).append("\"");
        }

        jsonBuilder.append(",\"active\":").append(userAuthDTO.isActive());

        if (userAuthDTO.getTenantId() != null) {
            jsonBuilder.append(",\"tenantId\":[\"java.lang.Long\",").append(userAuthDTO.getTenantId()).append("]");
        }

        if (userAuthDTO.getSalt() != null) {
            jsonBuilder.append(",\"salt\":\"").append(escapeJson(userAuthDTO.getSalt())).append("\"");
        }

        jsonBuilder.append("}");

        return jsonBuilder.toString();
    }

    /**
     * 从JsonNode中提取Long值
     * 支持直接数值和数组格式 ["java.lang.Long", value]
     *
     * @param node JsonNode
     * @return Long值，如果无法解析则返回null
     */
    private Long extractLongValue(JsonNode node) {
        if (node == null) {
            return null;
        }

        if (node.isNumber()) {
            // 直接数值格式
            return node.asLong();
        } else if (node.isArray() && node.size() == 2) {
            // 数组格式 ["java.lang.Long", value]
            JsonNode typeNode = node.get(0);
            JsonNode valueNode = node.get(1);

            if (typeNode.isTextual() && "java.lang.Long".equals(typeNode.asText()) && valueNode.isNumber()) {
                return valueNode.asLong();
            }
        } else if (node.isTextual()) {
            // 字符串格式，尝试解析为Long
            try {
                return Long.parseLong(node.asText());
            } catch (NumberFormatException e) {
                LOG.warn("Failed to parse Long value from text: {}", node.asText());
            }
        }

        LOG.warn("Unable to extract Long value from node: {}", node);
        return null;
    }

    /**
     * 转义JSON字符串中的特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeJson(String str) {
        if (str == null) {
            return null;
        }
        return str.replace("\\", "\\\\")
                  .replace("\"", "\\\"")
                  .replace("\n", "\\n")
                  .replace("\r", "\\r")
                  .replace("\t", "\\t");
    }

}
