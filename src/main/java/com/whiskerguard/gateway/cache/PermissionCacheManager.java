package com.whiskerguard.gateway.cache;

import com.whiskerguard.gateway.config.LicensePermissionProperties;
import java.time.Duration;
import java.util.Set;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 权限缓存管理器
 * 使用Redis缓存权限验证结果以提高性能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
@Component
public class PermissionCacheManager {

    private static final Logger log = LoggerFactory.getLogger(PermissionCacheManager.class);

    private static final String CACHE_KEY_PREFIX = "gateway:permission:";
    private static final String TENANT_CACHE_PATTERN = CACHE_KEY_PREFIX + "%s:*";

    private final ReactiveRedisTemplate<String, Object> redisTemplate;
    private final LicensePermissionProperties properties;

    public PermissionCacheManager(ReactiveRedisTemplate<String, Object> redisTemplate, LicensePermissionProperties properties) {
        this.redisTemplate = redisTemplate;
        this.properties = properties;
    }

    /**
     * 获取缓存的权限信息
     *
     * @param tenantId    租户ID
     * @param featureCode 功能权限编码
     * @return 权限信息，如果缓存未命中则返回null
     */
    public Mono<Boolean> getPermission(String tenantId, String featureCode) {
        if (!isCacheEnabled()) {
            log.debug("Cache is disabled, skipping cache lookup for tenant: {}, feature: {}", tenantId, featureCode);
            return Mono.empty();
        }

        String key = buildCacheKey(tenantId, featureCode);
        return redisTemplate
            .opsForValue()
            .get(key)
            .cast(Boolean.class)
            .doOnNext(result -> log.info("Cache hit for key: {}, result: {}", key, result))
            .doOnError(error -> log.warn("Error reading from cache for key: {}, error: {}", key, error.getMessage()))
            .onErrorResume(throwable -> {
                log.warn("Cache read failed for key: {}, falling back to service call", key);
                return Mono.empty();
            });
    }

    /**
     * 缓存权限信息
     *
     * @param tenantId      租户ID
     * @param featureCode   功能权限编码
     * @param hasPermission 是否有权限
     * @return 缓存操作结果
     */
    public Mono<Boolean> cachePermission(String tenantId, String featureCode, Boolean hasPermission) {
        if (!isCacheEnabled()) {
            return Mono.just(true);
        }

        String key = buildCacheKey(tenantId, featureCode);
        Duration ttl = Duration.ofSeconds(properties.getCache().getTtl());

        return redisTemplate
            .opsForValue()
            .set(key, hasPermission, ttl)
            .doOnNext(result -> {
                if (result) {
                    log.debug("Cached permission for key: {}, value: {}, ttl: {}s", key, hasPermission, properties.getCache().getTtl());
                } else {
                    log.warn("Failed to cache permission for key: {}", key);
                }
            })
            .doOnError(error -> log.warn("Error caching permission for key: {}, error: {}", key, error.getMessage()))
            .onErrorReturn(false);
    }

    /**
     * 清除租户的所有权限缓存
     *
     * @param tenantId 租户ID，支持通配符 "*" 清除所有缓存
     * @return 清除操作结果
     */
    public Mono<Long> clearTenantCache(String tenantId) {
        if (!isCacheEnabled()) {
            return Mono.just(0L);
        }

        String pattern;
        if ("*".equals(tenantId)) {
            // 清除所有权限缓存
            pattern = CACHE_KEY_PREFIX + "*";
            log.warn("Clearing ALL permission cache with pattern: {}", pattern);
        } else {
            pattern = String.format(TENANT_CACHE_PATTERN, tenantId);
            log.info("Clearing cache for tenant: {} with pattern: {}", tenantId, pattern);
        }

        return redisTemplate
            .keys(pattern)
            .collectList()
            .flatMap(keys -> {
                if (keys.isEmpty()) {
                    log.debug("No cache keys found for pattern: {}", pattern);
                    return Mono.just(0L);
                }

                log.info("Clearing {} cache keys for pattern: {}", keys.size(), pattern);
                return redisTemplate.delete(keys.toArray(new String[0]));
            })
            .doOnNext(deletedCount -> log.info("Cleared {} cache entries for pattern: {}", deletedCount, pattern))
            .doOnError(error -> log.warn("Error clearing cache for pattern: {}, error: {}", pattern, error.getMessage()))
            .onErrorReturn(0L);
    }

    /**
     * 清除指定权限的缓存
     *
     * @param tenantId    租户ID
     * @param featureCode 功能权限编码
     * @return 清除操作结果
     */
    public Mono<Boolean> clearPermissionCache(String tenantId, String featureCode) {
        if (!isCacheEnabled()) {
            return Mono.just(true);
        }

        String key = buildCacheKey(tenantId, featureCode);
        return redisTemplate
            .delete(key)
            .map(deletedCount -> deletedCount > 0)
            .doOnNext(result -> {
                if (result) {
                    log.debug("Cleared cache for key: {}", key);
                } else {
                    log.debug("No cache entry found for key: {}", key);
                }
            })
            .doOnError(error -> log.warn("Error clearing cache for key: {}, error: {}", key, error.getMessage()))
            .onErrorReturn(false);
    }

    /**
     * 获取缓存统计信息
     *
     * @param tenantId 租户ID
     * @return 缓存键数量
     */
    public Mono<Long> getCacheStats(String tenantId) {
        if (!isCacheEnabled()) {
            return Mono.just(0L);
        }

        String pattern = String.format(TENANT_CACHE_PATTERN, tenantId);
        return redisTemplate
            .keys(pattern)
            .count()
            .doOnNext(count -> log.debug("Cache stats for tenant {}: {} keys", tenantId, count))
            .onErrorReturn(0L);
    }

    /**
     * 构建缓存键
     *
     * @param tenantId    租户ID
     * @param featureCode 功能权限编码
     * @return 缓存键
     */
    private String buildCacheKey(String tenantId, String featureCode) {
        return CACHE_KEY_PREFIX + tenantId + ":" + featureCode;
    }

    /**
     * 检查缓存是否启用
     *
     * @return 是否启用缓存
     */
    private boolean isCacheEnabled() {
        return properties.getCache().isEnabled();
    }
}
