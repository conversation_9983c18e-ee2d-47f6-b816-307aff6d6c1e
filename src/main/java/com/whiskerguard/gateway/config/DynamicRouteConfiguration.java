package com.whiskerguard.gateway.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.gateway.filter.UserInfoGatewayFilterFactory;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.data.redis.connection.ReactiveRedisConnectionFactory;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

@Configuration
public class DynamicRouteConfiguration {

    private static final Logger log = LoggerFactory.getLogger(DynamicRouteConfiguration.class);
    private static final String REDIS_ROUTE_KEY = "gateway:routes";
    private final AtomicBoolean isInitialized = new AtomicBoolean(false);

    private final RouteLocatorBuilder routeLocatorBuilder;
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;
    private final ApplicationEventPublisher publisher;
    private final ReactiveRedisConnectionFactory redisConnectionFactory;
    private final ConsulRouteProperties consulRouteProperties;
    private final com.ecwid.consul.v1.ConsulClient consulClient;
    private final UserInfoGatewayFilterFactory userInfoGatewayFilterFactory;

    public DynamicRouteConfiguration(
        RouteLocatorBuilder routeLocatorBuilder,
        ReactiveRedisTemplate<String, String> redisTemplate,
        ObjectMapper objectMapper,
        ApplicationEventPublisher publisher,
        Environment environment,
        DiscoveryClient discoveryClient,
        ReactiveRedisConnectionFactory redisConnectionFactory,
        ConsulRouteProperties consulRouteProperties,
        com.ecwid.consul.v1.ConsulClient consulClient,
        UserInfoGatewayFilterFactory userInfoGatewayFilterFactory
    ) {
        this.routeLocatorBuilder = routeLocatorBuilder;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.publisher = publisher;
        this.redisConnectionFactory = redisConnectionFactory;
        this.consulRouteProperties = consulRouteProperties;
        this.consulClient = consulClient;
        this.userInfoGatewayFilterFactory = userInfoGatewayFilterFactory;
    }

    @PostConstruct
    public void init() {
        log.info("Initializing DynamicRouteConfiguration");
        try {
            // 初始化时从 Consul 加载路由配置
            if (consulRouteProperties.isEnabled()) {
                loadRoutesFromConsul();
            }

            // 测试 Redis 连接
            redisTemplate
                .opsForValue()
                .get(REDIS_ROUTE_KEY)
                .doOnError(e -> log.error("Failed to connect to Redis: {}", e.getMessage()))
                .subscribe();

            isInitialized.set(true);
        } catch (Exception e) {
            log.error("Error initializing route configuration: {}", e.getMessage());
        }
    }

    @PreDestroy
    public void destroy() {
        log.info("Shutting down DynamicRouteConfiguration");
        try {
            redisConnectionFactory.getReactiveConnection().close();
        } catch (Exception e) {
            log.error("Error closing Redis connection: {}", e.getMessage());
        }
    }

    @Scheduled(fixedDelayString = "${consul.route.watch-delay:1000}")
    public void watchConsulRoutes() {
        if (!consulRouteProperties.isEnabled() || !consulRouteProperties.isWatchEnabled()) {
            return;
        }

        try {
            loadRoutesFromConsul();
        } catch (Exception e) {
            log.error("Error watching Consul routes: {}", e.getMessage());
        }
    }

    private void loadRoutesFromConsul() {
        try {
            var response = consulClient.getKVValue(consulRouteProperties.getRouteKey());
            var value = response.getValue();
            if (value == null) {
//                log.warn("No route config found in Consul for key: {}", consulRouteProperties.getRouteKey());
                return;
            }
            String routesJson = value.getDecodedValue();
            if (routesJson != null && !routesJson.isEmpty()) {
                List<RouteDefinition> routes = objectMapper.readValue(routesJson, new TypeReference<List<RouteDefinition>>() {});

                // 同步到 Redis
                redisTemplate
                    .opsForValue()
                    .set(REDIS_ROUTE_KEY, routesJson)
                    .doOnSuccess(v -> log.info("Routes synchronized from Consul to Redis"))
                    .doOnError(e -> log.error("Failed to sync routes to Redis: {}", e.getMessage()))
                    .subscribe();

                // 刷新路由
                refreshRoutes();
            }
        } catch (Exception e) {
            log.error("Error loading routes from Consul: {}", e.getMessage());
        }
    }

    @Bean
    public RouteLocator dynamicRouteLocator() {
        return () -> {
            if (!isInitialized.get()) {
                return Flux.empty();
            }

            return redisTemplate
                .opsForValue()
                .get(REDIS_ROUTE_KEY)
                .flatMap(routesJson -> {
                    try {
                        List<RouteDefinition> routes = objectMapper.readValue(routesJson, new TypeReference<List<RouteDefinition>>() {});
                        return Mono.just(routes);
                    } catch (Exception e) {
                        log.error("Error parsing routes from Redis: {}", e.getMessage());
                        return Mono.just(new ArrayList<RouteDefinition>());
                    }
                })
                .flatMapMany(routes -> {
                    RouteLocatorBuilder.Builder routesBuilder = routeLocatorBuilder.routes();

                    for (RouteDefinition route : routes) {
                        if (route.isEnabled()) {
                            routesBuilder.route(route.getId(), r ->
                                r
                                    .predicate(exchange -> {
                                        String path = exchange.getRequest().getURI().getPath();
                                        return path.matches(route.getPath().replace("**", ".*"));
                                    })
                                    .filters(f -> {
                                        // 添加通用过滤器
                                        f.filter(userInfoGatewayFilterFactory.apply(new UserInfoGatewayFilterFactory.Config()));
                                        // 添加自定义过滤器
                                        if (route.getFilters() != null) {
                                            route.getFilters().forEach((name, args) -> {});
                                        }
                                        return f;
                                    })
                                    .uri("lb://" + route.getService())
                            );
                        }
                    }

                    return routesBuilder.build().getRoutes();
                })
                .onErrorResume(e -> {
                    log.error("Error building routes: {}", e.getMessage());
                    return Flux.empty();
                });
        };
    }

    public void refreshRoutes() {
        publisher.publishEvent(new RefreshRoutesEvent(this));
    }

    public void saveRoutes(List<RouteDefinition> routes) {
        try {
            String routesJson = objectMapper.writeValueAsString(routes);

            // 保存到 Redis
            redisTemplate
                .opsForValue()
                .set(REDIS_ROUTE_KEY, routesJson)
                .doOnSuccess(v -> log.info("Routes saved to Redis"))
                .doOnError(e -> log.error("Failed to save routes to Redis: {}", e.getMessage()))
                .subscribe();

            // 保存到 Consul
            if (consulRouteProperties.isEnabled()) {
                consulClient.setKVValue(consulRouteProperties.getRouteKey(), routesJson);
                log.info("Routes saved to Consul");
            }

            // 刷新路由
            refreshRoutes();
        } catch (Exception e) {
            log.error("Error saving routes: {}", e.getMessage());
        }
    }
}
