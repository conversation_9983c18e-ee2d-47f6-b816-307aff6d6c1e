package com.whiskerguard.gateway.config;

import java.util.ArrayList;
import java.util.List;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * License权限验证配置属性
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
@Component
@ConfigurationProperties(prefix = "license.permission")
public class LicensePermissionProperties {

    /**
     * 是否启用权限验证
     */
    private boolean enabled = true;

    /**
     * 降级策略：当License服务不可用时是否允许访问
     */
    private boolean fallbackAllow = false;

    /**
     * 权限路径映射配置
     */
    private List<PermissionPathMapping> pathMappings = new ArrayList<>();

    /**
     * 缓存配置
     */
    private CacheConfig cache = new CacheConfig();

    /**
     * 熔断器配置
     */
    private CircuitBreakerConfig circuitBreaker = new CircuitBreakerConfig();

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isFallbackAllow() {
        return fallbackAllow;
    }

    public void setFallbackAllow(boolean fallbackAllow) {
        this.fallbackAllow = fallbackAllow;
    }

    public List<PermissionPathMapping> getPathMappings() {
        return pathMappings;
    }

    public void setPathMappings(List<PermissionPathMapping> pathMappings) {
        this.pathMappings = pathMappings;
    }

    public CacheConfig getCache() {
        return cache;
    }

    public void setCache(CacheConfig cache) {
        this.cache = cache;
    }

    public CircuitBreakerConfig getCircuitBreaker() {
        return circuitBreaker;
    }

    public void setCircuitBreaker(CircuitBreakerConfig circuitBreaker) {
        this.circuitBreaker = circuitBreaker;
    }

    /**
     * 权限路径映射配置
     */
    public static class PermissionPathMapping {

        /**
         * 路径模式 (支持Ant风格通配符)
         */
        private String path;

        /**
         * 功能权限编码
         */
        private String feature;

        /**
         * 允许的HTTP方法列表 (支持 * 表示所有方法)
         */
        private List<String> methods = List.of("*");

        /**
         * 描述信息
         */
        private String description;

        public String getPath() {
            return path;
        }

        public void setPath(String path) {
            this.path = path;
        }

        public String getFeature() {
            return feature;
        }

        public void setFeature(String feature) {
            this.feature = feature;
        }

        public List<String> getMethods() {
            return methods;
        }

        public void setMethods(List<String> methods) {
            this.methods = methods;
        }

        public String getDescription() {
            return description;
        }

        public void setDescription(String description) {
            this.description = description;
        }

        @Override
        public String toString() {
            return (
                "PermissionPathMapping{" +
                "path='" +
                path +
                '\'' +
                ", feature='" +
                feature +
                '\'' +
                ", methods=" +
                methods +
                ", description='" +
                description +
                '\'' +
                '}'
            );
        }
    }

    /**
     * 缓存配置
     */
    public static class CacheConfig {

        /**
         * 是否启用缓存
         */
        private boolean enabled = true;

        /**
         * 缓存过期时间 (秒)
         */
        private int ttl = 300; // 5分钟

        /**
         * 最大缓存大小
         */
        private int maxSize = 1000;

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }

        public int getTtl() {
            return ttl;
        }

        public void setTtl(int ttl) {
            this.ttl = ttl;
        }

        public int getMaxSize() {
            return maxSize;
        }

        public void setMaxSize(int maxSize) {
            this.maxSize = maxSize;
        }

        @Override
        public String toString() {
            return "CacheConfig{" + "enabled=" + enabled + ", ttl=" + ttl + ", maxSize=" + maxSize + '}';
        }
    }

    /**
     * 熔断器配置
     */
    public static class CircuitBreakerConfig {

        /**
         * 失败率阈值 (百分比)
         */
        private int failureRateThreshold = 50;

        /**
         * 熔断器打开状态等待时间 (秒)
         */
        private int waitDurationInOpenState = 30;

        /**
         * 半开状态允许的调用次数
         */
        private int permittedNumberOfCallsInHalfOpenState = 10;

        /**
         * 滑动窗口大小
         */
        private int slidingWindowSize = 100;

        public int getFailureRateThreshold() {
            return failureRateThreshold;
        }

        public void setFailureRateThreshold(int failureRateThreshold) {
            this.failureRateThreshold = failureRateThreshold;
        }

        public int getWaitDurationInOpenState() {
            return waitDurationInOpenState;
        }

        public void setWaitDurationInOpenState(int waitDurationInOpenState) {
            this.waitDurationInOpenState = waitDurationInOpenState;
        }

        public int getPermittedNumberOfCallsInHalfOpenState() {
            return permittedNumberOfCallsInHalfOpenState;
        }

        public void setPermittedNumberOfCallsInHalfOpenState(int permittedNumberOfCallsInHalfOpenState) {
            this.permittedNumberOfCallsInHalfOpenState = permittedNumberOfCallsInHalfOpenState;
        }

        public int getSlidingWindowSize() {
            return slidingWindowSize;
        }

        public void setSlidingWindowSize(int slidingWindowSize) {
            this.slidingWindowSize = slidingWindowSize;
        }

        @Override
        public String toString() {
            return (
                "CircuitBreakerConfig{" +
                "failureRateThreshold=" +
                failureRateThreshold +
                ", waitDurationInOpenState=" +
                waitDurationInOpenState +
                ", permittedNumberOfCallsInHalfOpenState=" +
                permittedNumberOfCallsInHalfOpenState +
                ", slidingWindowSize=" +
                slidingWindowSize +
                '}'
            );
        }
    }

    @Override
    public String toString() {
        return (
            "LicensePermissionProperties{" +
            "enabled=" +
            enabled +
            ", fallbackAllow=" +
            fallbackAllow +
            ", pathMappings=" +
            pathMappings +
            ", cache=" +
            cache +
            ", circuitBreaker=" +
            circuitBreaker +
            '}'
        );
    }
}
