package com.whiskerguard.gateway.config;

import io.github.resilience4j.retry.RetryConfig;
import io.github.resilience4j.retry.RetryRegistry;
import java.time.Duration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration for retry mechanism.
 * Provides retry configuration for different services with exponential backoff.
 */
@Configuration
public class RetryConfiguration {

    private static final Logger logger = LoggerFactory.getLogger(RetryConfiguration.class);
    private final RetryProperties retryProperties;

    public RetryConfiguration(RetryProperties retryProperties) {
        this.retryProperties = retryProperties;
    }

    @Bean
    public RetryRegistry retryRegistry() {
        RetryRegistry registry = RetryRegistry.ofDefaults();

        // 配置默认重试策略
        RetryConfig defaultConfig = createRetryConfig(retryProperties.getDefaultConfig());
        registry.addConfiguration("defaultConfig", defaultConfig);

        // 配置服务特定的重试策略
        retryProperties
            .getServices()
            .forEach((service, config) -> {
                RetryConfig serviceConfig = createRetryConfig(config);
                registry.addConfiguration(service, serviceConfig);
                logger.info("Configured retry for service: {} with max attempts: {}", service, config.getMaxAttempts());
            });

        return registry;
    }

    private RetryConfig createRetryConfig(RetryProperties.RetryConfig config) {
        return RetryConfig.<Object>custom()
            .maxAttempts(config.getMaxAttempts())
            .waitDuration(Duration.ofMillis(config.getInitialInterval()))
            .intervalBiFunction((attempt, result) -> {
                long interval = (long) (config.getInitialInterval() * Math.pow(config.getMultiplier(), attempt - 1));
                // 添加随机抖动
                double jitter = Math.random() * config.getJitter();
                return Math.min(interval + (long) jitter, config.getMaxInterval());
            })
            .retryOnException(throwable -> true)
            .build();
    }
}
