package com.whiskerguard.gateway.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for rate limiting.
 * Supports dynamic configuration updates through Spring Cloud Config.
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "rate-limiter")
public class RateLimiterProperties {

    private Map<String, Rule> rules = new HashMap<>();

    public Map<String, Rule> getRules() {
        return rules;
    }

    public void setRules(Map<String, Rule> rules) {
        this.rules = rules;
    }

    /**
     * Enhanced rule configuration for rate limiting.
     * Supports advanced features like different algorithms, retry mechanisms, and fallback options.
     */
    public static class Rule {

        private int replenishRate = 100;
        private int burstCapacity = 100;
        private String type = "GLOBAL";
        private int priority = 1;

        // Enhanced configuration options
        private int timeWindow = 1; // in seconds
        private String algorithm = "TOKEN_BUCKET"; // TOKEN_BUCKET, SLIDING_WINDOW, FIXED_WINDOW
        private boolean enableRetry = true;
        private int maxRetryAttempts = 3;
        private double retryBackoffMultiplier = 2.0;
        private boolean enableLocalFallback = true;
        private boolean enableDetailedLogging = false;
        private String customErrorMessage = "Rate limit exceeded. Please try again later.";

        // Health check and monitoring
        private boolean enableHealthCheck = true;
        private int healthCheckInterval = 30; // in seconds
        private double failureThreshold = 0.5; // 50% failure rate threshold

        // Path-specific configuration
        private String pathPattern = "/**";
        private String[] excludePaths = {};
        private String[] includeMethods = {"GET", "POST", "PUT", "DELETE", "PATCH"};

        // Getters and setters
        public int getReplenishRate() {
            return replenishRate;
        }

        public void setReplenishRate(int replenishRate) {
            this.replenishRate = Math.max(1, replenishRate);
        }

        public int getBurstCapacity() {
            return burstCapacity;
        }

        public void setBurstCapacity(int burstCapacity) {
            this.burstCapacity = Math.max(1, burstCapacity);
        }

        public String getType() {
            return type;
        }

        public void setType(String type) {
            this.type = type != null ? type.toUpperCase() : "GLOBAL";
        }

        public int getPriority() {
            return priority;
        }

        public void setPriority(int priority) {
            this.priority = Math.max(1, priority);
        }

        public int getTimeWindow() {
            return timeWindow;
        }

        public void setTimeWindow(int timeWindow) {
            this.timeWindow = Math.max(1, timeWindow);
        }

        public String getAlgorithm() {
            return algorithm;
        }

        public void setAlgorithm(String algorithm) {
            this.algorithm = algorithm != null ? algorithm.toUpperCase() : "TOKEN_BUCKET";
        }

        public boolean isEnableRetry() {
            return enableRetry;
        }

        public void setEnableRetry(boolean enableRetry) {
            this.enableRetry = enableRetry;
        }

        public int getMaxRetryAttempts() {
            return maxRetryAttempts;
        }

        public void setMaxRetryAttempts(int maxRetryAttempts) {
            this.maxRetryAttempts = Math.max(0, Math.min(10, maxRetryAttempts));
        }

        public double getRetryBackoffMultiplier() {
            return retryBackoffMultiplier;
        }

        public void setRetryBackoffMultiplier(double retryBackoffMultiplier) {
            this.retryBackoffMultiplier = Math.max(1.0, Math.min(10.0, retryBackoffMultiplier));
        }

        public boolean isEnableLocalFallback() {
            return enableLocalFallback;
        }

        public void setEnableLocalFallback(boolean enableLocalFallback) {
            this.enableLocalFallback = enableLocalFallback;
        }

        public boolean isEnableDetailedLogging() {
            return enableDetailedLogging;
        }

        public void setEnableDetailedLogging(boolean enableDetailedLogging) {
            this.enableDetailedLogging = enableDetailedLogging;
        }

        public String getCustomErrorMessage() {
            return customErrorMessage;
        }

        public void setCustomErrorMessage(String customErrorMessage) {
            this.customErrorMessage = customErrorMessage != null ? customErrorMessage : "Rate limit exceeded";
        }

        public boolean isEnableHealthCheck() {
            return enableHealthCheck;
        }

        public void setEnableHealthCheck(boolean enableHealthCheck) {
            this.enableHealthCheck = enableHealthCheck;
        }

        public int getHealthCheckInterval() {
            return healthCheckInterval;
        }

        public void setHealthCheckInterval(int healthCheckInterval) {
            this.healthCheckInterval = Math.max(10, healthCheckInterval);
        }

        public double getFailureThreshold() {
            return failureThreshold;
        }

        public void setFailureThreshold(double failureThreshold) {
            this.failureThreshold = Math.max(0.0, Math.min(1.0, failureThreshold));
        }

        public String getPathPattern() {
            return pathPattern;
        }

        public void setPathPattern(String pathPattern) {
            this.pathPattern = pathPattern != null ? pathPattern : "/**";
        }

        public String[] getExcludePaths() {
            return excludePaths;
        }

        public void setExcludePaths(String[] excludePaths) {
            this.excludePaths = excludePaths != null ? excludePaths : new String[0];
        }

        public String[] getIncludeMethods() {
            return includeMethods;
        }

        public void setIncludeMethods(String[] includeMethods) {
            this.includeMethods = includeMethods != null ? includeMethods : new String[]{"GET", "POST", "PUT", "DELETE", "PATCH"};
        }

        @Override
        public String toString() {
            return "Rule{" +
                "replenishRate=" + replenishRate +
                ", burstCapacity=" + burstCapacity +
                ", type='" + type + '\'' +
                ", priority=" + priority +
                ", timeWindow=" + timeWindow +
                ", algorithm='" + algorithm + '\'' +
                ", enableRetry=" + enableRetry +
                ", maxRetryAttempts=" + maxRetryAttempts +
                ", retryBackoffMultiplier=" + retryBackoffMultiplier +
                ", enableLocalFallback=" + enableLocalFallback +
                ", enableDetailedLogging=" + enableDetailedLogging +
                ", pathPattern='" + pathPattern + '\'' +
                '}';
        }
    }
}
