package com.whiskerguard.gateway.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import reactivefeign.spring.config.EnableReactiveFeignClients;

/**
 * 描述：Feign 客户端配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/28
 */
@Configuration
@EnableReactiveFeignClients(basePackages = "com.whiskerguard.gateway.client")
public class FeignConfiguration {

   /**
     * ObjectMapper bean for Feign clients.
     * This ensures that the same ObjectMapper instance is used across all Feign clients.
     */
    @Bean
    public ObjectMapper feignObjectMapper() {
        ObjectMapper mapper = new ObjectMapper();
        mapper.findAndRegisterModules();
        return mapper;
    }

}
