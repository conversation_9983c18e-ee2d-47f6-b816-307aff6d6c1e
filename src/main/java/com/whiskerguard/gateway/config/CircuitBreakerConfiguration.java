package com.whiskerguard.gateway.config;

import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig;
import io.github.resilience4j.timelimiter.TimeLimiterConfig;
import java.time.Duration;
import org.springframework.cloud.circuitbreaker.resilience4j.ReactiveResilience4JCircuitBreakerFactory;
import org.springframework.cloud.circuitbreaker.resilience4j.Resilience4JConfigBuilder;
import org.springframework.cloud.client.circuitbreaker.Customizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CircuitBreakerConfiguration {

    @Bean
    public Customizer<ReactiveResilience4JCircuitBreakerFactory> defaultCustomizer() {
        return factory -> {
            factory.configureDefault(id ->
                new Resilience4JConfigBuilder(id)
                    .circuitBreakerConfig(
                        CircuitBreakerConfig.custom()
                            .failureRateThreshold(50)
                            .waitDurationInOpenState(Duration.ofSeconds(60))
                            .permittedNumberOfCallsInHalfOpenState(10)
                            .slidingWindowSize(100)
                            .build()
                    )
                    .timeLimiterConfig(TimeLimiterConfig.custom().timeoutDuration(Duration.ofSeconds(4)).build())
                    .build()
            );
        };
    }

    @Bean
    public Customizer<ReactiveResilience4JCircuitBreakerFactory> authServiceCustomizer() {
        return factory -> {
            factory.configure(
                builder ->
                    builder
                        .circuitBreakerConfig(
                            CircuitBreakerConfig.custom()
                                .failureRateThreshold(30)
                                .waitDurationInOpenState(Duration.ofSeconds(30))
                                .permittedNumberOfCallsInHalfOpenState(5)
                                .slidingWindowSize(50)
                                .build()
                        )
                        .timeLimiterConfig(TimeLimiterConfig.custom().timeoutDuration(Duration.ofSeconds(2)).build())
                        .build(),
                "whiskerguard-auth-service"
            );
        };
    }

    @Bean
    public Customizer<ReactiveResilience4JCircuitBreakerFactory> userServiceCustomizer() {
        return factory -> {
            factory.configure(
                builder ->
                    builder
                        .circuitBreakerConfig(
                            CircuitBreakerConfig.custom()
                                .failureRateThreshold(40)
                                .waitDurationInOpenState(Duration.ofSeconds(45))
                                .permittedNumberOfCallsInHalfOpenState(8)
                                .slidingWindowSize(80)
                                .build()
                        )
                        .timeLimiterConfig(TimeLimiterConfig.custom().timeoutDuration(Duration.ofSeconds(3)).build())
                        .build(),
                "user-service"
            );
        };
    }

    @Bean
    public Customizer<ReactiveResilience4JCircuitBreakerFactory> licenseServiceCustomizer() {
        return factory -> {
            factory.configure(
                builder ->
                    builder
                        .circuitBreakerConfig(
                            CircuitBreakerConfig.custom()
                                .failureRateThreshold(50)
                                .waitDurationInOpenState(Duration.ofSeconds(30))
                                .permittedNumberOfCallsInHalfOpenState(10)
                                .slidingWindowSize(100)
                                .build()
                        )
                        .timeLimiterConfig(TimeLimiterConfig.custom().timeoutDuration(Duration.ofSeconds(5)).build())
                        .build(),
                "license-service"
            );
        };
    }
}
