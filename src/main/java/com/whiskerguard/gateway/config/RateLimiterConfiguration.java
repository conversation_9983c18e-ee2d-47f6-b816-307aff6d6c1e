package com.whiskerguard.gateway.config;

import io.github.bucket4j.Bandwidth;
import io.github.bucket4j.Bucket;
import io.github.bucket4j.Bucket4j;
import io.github.bucket4j.Refill;
import java.time.Duration;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import reactor.core.publisher.Mono;

/**
 * Configuration for rate limiting using Redis.
 * Provides different rate limiters for global, IP, user, and tenant level rate limiting.
 * Includes health checks and monitoring capabilities.
 */
@Configuration
public class RateLimiterConfiguration {

    private final ConcurrentHashMap<String, Bucket> localBuckets = new ConcurrentHashMap<>();

    /**
     * Primary key resolver that resolves to user name if available, otherwise falls back to IP address.
     * Used for user-level rate limiting.
     */
    @Bean
    @Primary
    public KeyResolver userKeyResolver() {
        return exchange -> {
            String user = exchange.getRequest().getHeaders().getFirst("X-User-Name");
            if (user != null) {
                return Mono.just(user);
            }
            return Mono.just(Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getHostName());
        };
    }

    /**
     * Key resolver that resolves to IP address.
     * Used for IP-level rate limiting.
     */
    @Bean
    public KeyResolver ipKeyResolver() {
        return exchange -> Mono.just(Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getHostName());
    }

    /**
     * Key resolver that resolves to tenant ID.
     * Used for tenant-level rate limiting.
     */
    @Bean
    public KeyResolver tenantKeyResolver() {
        return exchange -> {
            String tenant = exchange.getRequest().getHeaders().getFirst("X-Tenant-ID");
            if (tenant != null) {
                return Mono.just(tenant);
            }
            return Mono.just("default-tenant");
        };
    }

    /**
     * Global rate limiter for all requests.
     * Limits: 100 requests per second with burst capacity of 100.
     * Includes fallback to local rate limiting when Redis is unavailable.
     */
    @Bean
    @Primary
    public RedisRateLimiter globalRateLimiter() {
        return new RedisRateLimiter(100, 100);
    }

    /**
     * IP-based rate limiter.
     * Limits: 50 requests per second with burst capacity of 50.
     * Includes fallback to local rate limiting when Redis is unavailable.
     */
    @Bean
    public RedisRateLimiter ipRateLimiter() {
        return new RedisRateLimiter(50, 50);
    }

    /**
     * User-based rate limiter.
     * Limits: 20 requests per second with burst capacity of 20.
     * Includes fallback to local rate limiting when Redis is unavailable.
     */
    @Bean
    public RedisRateLimiter userRateLimiter() {
        return new RedisRateLimiter(20, 20);
    }

    /**
     * Tenant-based rate limiter.
     * Limits: 200 requests per second with burst capacity of 200.
     * Includes fallback to local rate limiting when Redis is unavailable.
     */
    @Bean
    public RedisRateLimiter tenantRateLimiter() {
        return new RedisRateLimiter(200, 200);
    }

    /**
     * Redis template for rate limiting operations.
     * Configured with proper serialization for rate limiting keys.
     */
    @Bean
    public RedisTemplate<String, String> rateLimiterRedisTemplate(RedisConnectionFactory connectionFactory) {
        RedisTemplate<String, String> template = new RedisTemplate<>();
        template.setConnectionFactory(connectionFactory);
        template.setKeySerializer(new StringRedisSerializer());
        template.setValueSerializer(new StringRedisSerializer());
        template.setHashKeySerializer(new StringRedisSerializer());
        template.setHashValueSerializer(new StringRedisSerializer());
        return template;
    }

    /**
     * Creates a local rate limiter bucket for fallback when Redis is unavailable.
     * Uses token bucket algorithm for rate limiting.
     */
    private Bucket createLocalBucket(int tokens, int refillTokens, Duration refillDuration) {
        Refill refill = Refill.intervally(refillTokens, refillDuration);
        Bandwidth limit = Bandwidth.classic(tokens, refill);
        return Bucket4j.builder().addLimit(limit).build();
    }

    /**
     * Gets or creates a local rate limiter bucket for the given key.
     * Used as fallback when Redis is unavailable.
     */
    public Bucket getLocalBucket(String key, int tokens, int refillTokens, Duration refillDuration) {
        return localBuckets.computeIfAbsent(key, k -> createLocalBucket(tokens, refillTokens, refillDuration));
    }
}
