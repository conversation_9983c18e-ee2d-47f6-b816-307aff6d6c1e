package com.whiskerguard.gateway.config;

import com.whiskerguard.gateway.filter.GlobalRateLimiterGatewayFilterFactory;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.cloud.context.environment.EnvironmentChangeEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 限流器组件配置管理器。
 * 处理动态配置更新、验证和热重载功能。
 *
 * 主要功能特性：
 * - 无需重启的动态配置更新
 * - 配置验证和数据清理
 * - 配置变更事件处理
 * - 配置变更指标收集
 * - 无效配置的回滚支持
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
public class RateLimiterConfigurationManager {

    private static final Logger log = LoggerFactory.getLogger(RateLimiterConfigurationManager.class);

    private final RateLimiterProperties rateLimiterProperties;
    private final MeterRegistry meterRegistry;

    // 配置缓存和验证
    private final Map<String, RateLimiterProperties.Rule> activeRules = new ConcurrentHashMap<>();
    private final Map<String, RateLimiterProperties.Rule> previousRules = new ConcurrentHashMap<>();

    // 指标收集
    private final Counter configurationUpdatesCounter;
    private final Counter configurationErrorsCounter;
    private final Counter configurationRollbacksCounter;

    // 配置验证规则
    private static final int MIN_REPLENISH_RATE = 1;
    private static final int MAX_REPLENISH_RATE = 10000;
    private static final int MIN_BURST_CAPACITY = 1;
    private static final int MAX_BURST_CAPACITY = 50000;
    private static final int MIN_TIME_WINDOW = 1;
    private static final int MAX_TIME_WINDOW = 3600; // 1 hour
    private static final int MIN_RETRY_ATTEMPTS = 0;
    private static final int MAX_RETRY_ATTEMPTS = 10;
    private static final double MIN_BACKOFF_MULTIPLIER = 1.0;
    private static final double MAX_BACKOFF_MULTIPLIER = 10.0;

    public RateLimiterConfigurationManager(
        RateLimiterProperties rateLimiterProperties,
        MeterRegistry meterRegistry
    ) {
        this.rateLimiterProperties = rateLimiterProperties;
        this.meterRegistry = meterRegistry;

        // 初始化指标
        this.configurationUpdatesCounter = Counter.builder("rate_limiter_config_updates_total")
            .description("配置更新总次数")
            .register(meterRegistry);
        this.configurationErrorsCounter = Counter.builder("rate_limiter_config_errors_total")
            .description("配置错误总次数")
            .register(meterRegistry);
        this.configurationRollbacksCounter = Counter.builder("rate_limiter_config_rollbacks_total")
            .description("配置回滚总次数")
            .register(meterRegistry);

        log.info("限流器配置管理器已初始化");
    }

    /**
     * 处理应用程序就绪事件以初始化配置。
     */
    @EventListener
    public void handleApplicationReady(ApplicationReadyEvent event) {
        log.info("应用程序就绪，正在初始化限流器配置");
        initializeConfiguration();
    }

    /**
     * 处理环境变更事件以进行动态配置更新。
     */
    @EventListener
    public void handleEnvironmentChange(EnvironmentChangeEvent event) {
        log.info("检测到环境变更，正在更新限流器配置");

        // 检查限流器配置是否发生变更
        boolean rateLimiterConfigChanged = event.getKeys().stream()
            .anyMatch(key -> key.startsWith("rate-limiter"));

        if (rateLimiterConfigChanged) {
            updateConfiguration();
        }
    }

    /**
     * 在启动时初始化配置。
     */
    private void initializeConfiguration() {
        try {
            Map<String, RateLimiterProperties.Rule> rules = rateLimiterProperties.getRules();

            if (rules == null || rules.isEmpty()) {
                log.warn("未配置限流规则，使用默认配置");
                createDefaultRules();
                return;
            }

            // 验证并存储初始配置
            for (Map.Entry<String, RateLimiterProperties.Rule> entry : rules.entrySet()) {
                String ruleName = entry.getKey();
                RateLimiterProperties.Rule rule = entry.getValue();

                if (validateRule(ruleName, rule)) {
                    activeRules.put(ruleName, rule);
                    log.info("已初始化限流规则: {} 配置: {}", ruleName, rule);
                } else {
                    log.error("无效的限流规则配置: {}，跳过", ruleName);
                    configurationErrorsCounter.increment();
                }
            }

            log.info("限流器配置已初始化，共 {} 条规则", activeRules.size());

        } catch (Exception e) {
            log.error("初始化限流器配置时发生错误", e);
            configurationErrorsCounter.increment();
            createDefaultRules();
        }
    }

    /**
     * 动态更新配置。
     */
    private void updateConfiguration() {
        try {
            // 备份当前配置
            previousRules.clear();
            previousRules.putAll(activeRules);

            Map<String, RateLimiterProperties.Rule> newRules = rateLimiterProperties.getRules();
            Map<String, RateLimiterProperties.Rule> validatedRules = new ConcurrentHashMap<>();

            // 验证新配置
            boolean allValid = true;
            for (Map.Entry<String, RateLimiterProperties.Rule> entry : newRules.entrySet()) {
                String ruleName = entry.getKey();
                RateLimiterProperties.Rule rule = entry.getValue();

                if (validateRule(ruleName, rule)) {
                    validatedRules.put(ruleName, rule);
                } else {
                    log.error("规则配置无效: {}，将执行回滚", ruleName);
                    allValid = false;
                    break;
                }
            }

            if (allValid) {
                // 应用新配置
                activeRules.clear();
                activeRules.putAll(validatedRules);

                configurationUpdatesCounter.increment();
                log.info("限流器配置更新成功，共 {} 条规则", activeRules.size());

                // 记录变更
                logConfigurationChanges(previousRules, activeRules);

            } else {
                // 回滚到之前的配置
                rollbackConfiguration();
            }

        } catch (Exception e) {
            log.error("更新限流器配置时发生错误", e);
            configurationErrorsCounter.increment();
            rollbackConfiguration();
        }
    }

    /**
     * 回滚到之前的配置。
     */
    private void rollbackConfiguration() {
        if (!previousRules.isEmpty()) {
            activeRules.clear();
            activeRules.putAll(previousRules);
            configurationRollbacksCounter.increment();
            log.warn("已回滚到之前的限流器配置");
        } else {
            log.error("没有可用的之前配置进行回滚，使用默认配置");
            createDefaultRules();
        }
    }

    /**
     * 验证限流规则。
     */
    private boolean validateRule(String ruleName, RateLimiterProperties.Rule rule) {
        if (rule == null) {
            log.error("规则 {} 为空", ruleName);
            return false;
        }

        // Validate replenish rate
        if (rule.getReplenishRate() < MIN_REPLENISH_RATE || rule.getReplenishRate() > MAX_REPLENISH_RATE) {
            log.error("Invalid replenish rate for rule {}: {} (must be between {} and {})",
                ruleName, rule.getReplenishRate(), MIN_REPLENISH_RATE, MAX_REPLENISH_RATE);
            return false;
        }

        // Validate burst capacity
        if (rule.getBurstCapacity() < MIN_BURST_CAPACITY || rule.getBurstCapacity() > MAX_BURST_CAPACITY) {
            log.error("Invalid burst capacity for rule {}: {} (must be between {} and {})",
                ruleName, rule.getBurstCapacity(), MIN_BURST_CAPACITY, MAX_BURST_CAPACITY);
            return false;
        }

        // Validate time window
        if (rule.getTimeWindow() < MIN_TIME_WINDOW || rule.getTimeWindow() > MAX_TIME_WINDOW) {
            log.error("Invalid time window for rule {}: {} (must be between {} and {})",
                ruleName, rule.getTimeWindow(), MIN_TIME_WINDOW, MAX_TIME_WINDOW);
            return false;
        }

        // Validate retry attempts
        if (rule.getMaxRetryAttempts() < MIN_RETRY_ATTEMPTS || rule.getMaxRetryAttempts() > MAX_RETRY_ATTEMPTS) {
            log.error("Invalid max retry attempts for rule {}: {} (must be between {} and {})",
                ruleName, rule.getMaxRetryAttempts(), MIN_RETRY_ATTEMPTS, MAX_RETRY_ATTEMPTS);
            return false;
        }

        // Validate backoff multiplier
        if (rule.getRetryBackoffMultiplier() < MIN_BACKOFF_MULTIPLIER || rule.getRetryBackoffMultiplier() > MAX_BACKOFF_MULTIPLIER) {
            log.error("Invalid retry backoff multiplier for rule {}: {} (must be between {} and {})",
                ruleName, rule.getRetryBackoffMultiplier(), MIN_BACKOFF_MULTIPLIER, MAX_BACKOFF_MULTIPLIER);
            return false;
        }

        // Validate algorithm
        try {
            GlobalRateLimiterGatewayFilterFactory.Config.Algorithm.valueOf(rule.getAlgorithm());
        } catch (IllegalArgumentException e) {
            log.error("Invalid algorithm for rule {}: {}", ruleName, rule.getAlgorithm());
            return false;
        }

        // Validate type
        if (rule.getType() == null || rule.getType().trim().isEmpty()) {
            log.error("Invalid type for rule {}: {}", ruleName, rule.getType());
            return false;
        }

        // Validate path pattern
        if (rule.getPathPattern() == null || rule.getPathPattern().trim().isEmpty()) {
            log.error("Invalid path pattern for rule {}: {}", ruleName, rule.getPathPattern());
            return false;
        }

        log.debug("Rule {} validation passed", ruleName);
        return true;
    }

    /**
     * Creates default rules when no configuration is available.
     */
    private void createDefaultRules() {
        log.info("Creating default rate limiter rules");

        RateLimiterProperties.Rule globalRule = new RateLimiterProperties.Rule();
        globalRule.setReplenishRate(100);
        globalRule.setBurstCapacity(100);
        globalRule.setType("GLOBAL");
        globalRule.setPriority(1);
        globalRule.setTimeWindow(1);
        globalRule.setAlgorithm("TOKEN_BUCKET");
        globalRule.setEnableRetry(true);
        globalRule.setMaxRetryAttempts(3);
        globalRule.setRetryBackoffMultiplier(2.0);
        globalRule.setEnableLocalFallback(true);

        activeRules.put("global", globalRule);

        log.info("Default rate limiter configuration created");
    }

    /**
     * Logs configuration changes.
     */
    private void logConfigurationChanges(
        Map<String, RateLimiterProperties.Rule> oldRules,
        Map<String, RateLimiterProperties.Rule> newRules
    ) {
        // Log added rules
        newRules.keySet().stream()
            .filter(key -> !oldRules.containsKey(key))
            .forEach(key -> log.info("Added rate limiter rule: {} with config: {}", key, newRules.get(key)));

        // Log removed rules
        oldRules.keySet().stream()
            .filter(key -> !newRules.containsKey(key))
            .forEach(key -> log.info("Removed rate limiter rule: {}", key));

        // Log modified rules
        newRules.entrySet().stream()
            .filter(entry -> oldRules.containsKey(entry.getKey()))
            .filter(entry -> !entry.getValue().toString().equals(oldRules.get(entry.getKey()).toString()))
            .forEach(entry -> log.info("Modified rate limiter rule: {} from {} to {}",
                entry.getKey(), oldRules.get(entry.getKey()), entry.getValue()));
    }

    /**
     * Gets the current active rules.
     */
    public Map<String, RateLimiterProperties.Rule> getActiveRules() {
        return new ConcurrentHashMap<>(activeRules);
    }

    /**
     * Gets a specific rule by name.
     */
    public RateLimiterProperties.Rule getRule(String ruleName) {
        return activeRules.get(ruleName);
    }

    /**
     * Checks if a rule exists.
     */
    public boolean hasRule(String ruleName) {
        return activeRules.containsKey(ruleName);
    }
}
