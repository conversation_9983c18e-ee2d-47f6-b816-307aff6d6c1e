package com.whiskerguard.gateway.config;

import com.whiskerguard.gateway.security.AuthoritiesConstants;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.config.Customizer;
import org.springframework.security.config.annotation.method.configuration.EnableReactiveMethodSecurity;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.header.ReferrerPolicyServerHttpHeadersWriter;
import org.springframework.security.web.server.header.XFrameOptionsServerHttpHeadersWriter.Mode;
import tech.jhipster.config.JHipsterProperties;

@Configuration
@EnableReactiveMethodSecurity
@EnableWebFluxSecurity
@Order(0)
public class SecurityConfiguration {

    private final JHipsterProperties jHipsterProperties;

    public SecurityConfiguration(JHipsterProperties jHipsterProperties) {
        this.jHipsterProperties = jHipsterProperties;
    }

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        http.csrf(ServerHttpSecurity.CsrfSpec::disable)
            .headers(headers ->
                headers
                    .contentSecurityPolicy(csp -> csp.policyDirectives(jHipsterProperties.getSecurity().getContentSecurityPolicy()))
                    .frameOptions(frameOptions -> frameOptions.mode(Mode.DENY))
                    .referrerPolicy(referrer ->
                        referrer.policy(ReferrerPolicyServerHttpHeadersWriter.ReferrerPolicy.STRICT_ORIGIN_WHEN_CROSS_ORIGIN)
                    )
                    .permissionsPolicy(permissions ->
                        permissions.policy(
                            "camera=(), fullscreen=(self), geolocation=(), gyroscope=(), magnetometer=(), microphone=(), midi=(), payment=(), sync-xhr=()"
                        )
                    )
            )
            .authorizeExchange(authz ->
                authz
                    .pathMatchers("/api/auth/introspect")
                    .permitAll()
                    .pathMatchers("/api/authenticate")
                    .permitAll()
                    .pathMatchers("/api/oauth/token")
                    .permitAll()
                    .pathMatchers("/services/*/api/sessions/login")
                    .permitAll()
                    .pathMatchers("/services/*/api/wechat/callback")
                    .permitAll()
                    .pathMatchers("/services/*/api/employees/password/reset")
                    .permitAll()
                    .pathMatchers("/services/*/api/verification/code/send")
                    .permitAll()
                    .pathMatchers("/MP_verify_pMjb7EmECeca3bYh.txt")
                    .permitAll()
                    .pathMatchers("/services/*/api/reservations")
                    .permitAll()
                    .pathMatchers("/api/admin/**")
                    .hasAuthority(AuthoritiesConstants.ADMIN)
                    .pathMatchers("/services/*/management/health/readiness")
                    .permitAll()
                    .pathMatchers("/services/*/v3/api-docs")
                    .hasAuthority(AuthoritiesConstants.ADMIN)
                    .pathMatchers("/services/**")
                    .authenticated()
                    .pathMatchers("/v3/api-docs/**")
                    .hasAuthority(AuthoritiesConstants.ADMIN)
                    .pathMatchers("/management/health")
                    .permitAll()
                    .pathMatchers("/management/health/**")
                    .permitAll()
                    .pathMatchers("/management/info")
                    .permitAll()
                    .pathMatchers("/management/prometheus")
                    .permitAll()
                    .pathMatchers("/management/**")
                    .hasAuthority(AuthoritiesConstants.ADMIN)
                    .pathMatchers("/api/**")
                    .authenticated()
            )
            .oauth2ResourceServer(oauth2 -> oauth2
                .jwt(Customizer.withDefaults()));
        return http.build();
    }
}
