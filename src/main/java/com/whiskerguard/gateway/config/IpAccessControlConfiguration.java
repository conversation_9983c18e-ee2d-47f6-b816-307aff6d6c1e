package com.whiskerguard.gateway.config;

import jakarta.annotation.PostConstruct;
import java.net.InetAddress;
import java.util.List;
import java.util.regex.Pattern;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.MediaType;
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.RouterFunctions;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

@Configuration
public class IpAccessControlConfiguration {

    private static final Logger log = LoggerFactory.getLogger(IpAccessControlConfiguration.class);
    private final IpAccessControlProperties properties;

    public IpAccessControlConfiguration(IpAccessControlProperties properties) {
        this.properties = properties;
    }

    @PostConstruct
    public void init() {
        log.info("IP Access Control Properties loaded:");
        log.info("Whitelist mode: {}", properties.isWhitelistMode());
        log.info("Whitelist: {}", properties.getWhitelist());
        log.info("Blacklist: {}", properties.getBlacklist());
        log.info("Sensitive paths: {}", properties.getSensitivePaths());
    }

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder) {
        return builder
            .routes()
            // .route("api_route", r ->
            //     r
            //         .path("/api/**")
            //         .filters(f ->
            //             f
            //                 .addRequestHeader("X-Request-Id", "{{random.uuid}}")
            //                 .filter((exchange, chain) -> {
            //                     String clientIp = exchange.getRequest().getRemoteAddress().getAddress().getHostAddress();
            //                     String path = exchange.getRequest().getURI().getPath();
            //                     log.info("Access from IP: {}, path: {}", clientIp, path);

            //                     // 检查敏感路径
            //                     if (isSensitivePath(path) && !isIpInList(clientIp, whitelist)) {
            //                         log.warn("Blocked access to sensitive path from IP: {}", clientIp);
            //                         exchange.getResponse().setRawStatusCode(403);
            //                         return exchange.getResponse().setComplete();
            //                     }

            //                     // 检查IP访问控制
            //                     if (whitelistMode && !isIpInList(clientIp, whitelist)) {
            //                         log.warn("Blocked access from non-whitelisted IP: {}", clientIp);
            //                         exchange.getResponse().setRawStatusCode(403);
            //                         return exchange.getResponse().setComplete();
            //                     }

            //                     if (!whitelistMode && isIpInList(clientIp, blacklist)) {
            //                         log.warn("Blocked access from blacklisted IP: {}", clientIp);
            //                         exchange.getResponse().setRawStatusCode(403);
            //                         return exchange.getResponse().setComplete();
            //                     }

            //                     return chain.filter(exchange);
            //                 })
            //         )
            //         .uri("http://localhost:8080")
            // )
            .build();
    }

    private boolean isIpInList(String ip, List<String> ipList) {
        if (ipList == null || ipList.isEmpty()) {
            return false;
        }
        try {
            InetAddress clientAddress = InetAddress.getByName(ip);
            return ipList.stream().anyMatch(cidr -> isIpInCidr(clientAddress, cidr));
        } catch (Exception e) {
            log.error("Error checking IP address: {}", ip, e);
            return false;
        }
    }

    private boolean isIpInCidr(InetAddress ip, String cidr) {
        try {
            String[] parts = cidr.split("/");
            InetAddress networkAddress = InetAddress.getByName(parts[0]);
            int prefixLength = Integer.parseInt(parts[1]);

            byte[] ipBytes = ip.getAddress();
            byte[] networkBytes = networkAddress.getAddress();

            if (ipBytes.length != networkBytes.length) {
                return false;
            }

            int numBytes = prefixLength / 8;
            int remainingBits = prefixLength % 8;

            // 检查完整字节
            for (int i = 0; i < numBytes; i++) {
                if (ipBytes[i] != networkBytes[i]) {
                    return false;
                }
            }

            // 检查剩余位
            if (remainingBits > 0) {
                int mask = (0xFF << (8 - remainingBits)) & 0xFF;
                return (ipBytes[numBytes] & mask) == (networkBytes[numBytes] & mask);
            }

            return true;
        } catch (Exception e) {
            log.error("Error checking CIDR: {}", cidr, e);
            return false;
        }
    }

    private boolean isSensitivePath(String path) {
        if (properties.getSensitivePaths() == null || properties.getSensitivePaths().isEmpty()) {
            return false;
        }
        return properties.getSensitivePaths().stream().anyMatch(pattern -> Pattern.matches(pattern, path));
    }

    @Bean
    public RouterFunction<ServerResponse> testEndpoint() {
        return RouterFunctions.route()
            .GET("/api/test", request -> {
                log.info("Handling test endpoint request");
                return ServerResponse.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just("{\"message\": \"Test endpoint\"}"), String.class);
            })
            .GET("/api/admin/test", request -> {
                log.info("Handling admin test endpoint request");
                return ServerResponse.ok()
                    .contentType(MediaType.APPLICATION_JSON)
                    .body(Mono.just("{\"message\": \"Admin test endpoint\"}"), String.class);
            })
            .build();
    }
}
