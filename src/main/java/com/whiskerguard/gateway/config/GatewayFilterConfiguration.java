package com.whiskerguard.gateway.config;

import com.whiskerguard.gateway.filter.UserInfoGatewayFilterFactory;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.cloud.gateway.route.builder.RouteLocatorBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GatewayFilterConfiguration {

    @Bean
    public RouteLocator customRouteLocator(RouteLocatorBuilder builder, UserInfoGatewayFilterFactory userInfoFilter) {
        return builder
            .routes()
            // 认证服务路由
            .route("auth_service_route", r ->
                r
                    .path("/api/auth/**")
                    .filters(f -> f.filter(userInfoFilter.apply(new UserInfoGatewayFilterFactory.Config())).stripPrefix(1))
                    .uri("lb://whiskerguardauthservice")
            )
            .build();
    }
}
