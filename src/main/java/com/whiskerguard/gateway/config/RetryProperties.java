package com.whiskerguard.gateway.config;

import java.util.HashMap;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * Configuration properties for retry mechanism.
 * Supports dynamic configuration updates through Spring Cloud Config.
 */
@Component
@RefreshScope
@ConfigurationProperties(prefix = "retry")
public class RetryProperties {

    private Map<String, ServiceRetryConfig> services = new HashMap<>();
    private RetryConfig defaultConfig = new RetryConfig();

    public Map<String, ServiceRetryConfig> getServices() {
        return services;
    }

    public void setServices(Map<String, ServiceRetryConfig> services) {
        this.services = services;
    }

    public RetryConfig getDefaultConfig() {
        return defaultConfig;
    }

    public void setDefaultConfig(RetryConfig defaultConfig) {
        this.defaultConfig = defaultConfig;
    }

    public static class ServiceRetryConfig extends RetryConfig {

        private String serviceName;
        private boolean enabled = true;

        public String getServiceName() {
            return serviceName;
        }

        public void setServiceName(String serviceName) {
            this.serviceName = serviceName;
        }

        public boolean isEnabled() {
            return enabled;
        }

        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }

    public static class RetryConfig {

        private int maxAttempts = 3;
        private int initialInterval = 1000;
        private int maxInterval = 10000;
        private double multiplier = 2.0;
        private double jitter = 0.1;
        private String[] retryableStatuses = { "BAD_GATEWAY", "SERVICE_UNAVAILABLE", "GATEWAY_TIMEOUT" };
        private String[] retryableMethods = { "GET", "POST" };

        public int getMaxAttempts() {
            return maxAttempts;
        }

        public void setMaxAttempts(int maxAttempts) {
            this.maxAttempts = maxAttempts;
        }

        public int getInitialInterval() {
            return initialInterval;
        }

        public void setInitialInterval(int initialInterval) {
            this.initialInterval = initialInterval;
        }

        public int getMaxInterval() {
            return maxInterval;
        }

        public void setMaxInterval(int maxInterval) {
            this.maxInterval = maxInterval;
        }

        public double getMultiplier() {
            return multiplier;
        }

        public void setMultiplier(double multiplier) {
            this.multiplier = multiplier;
        }

        public double getJitter() {
            return jitter;
        }

        public void setJitter(double jitter) {
            this.jitter = jitter;
        }

        public String[] getRetryableStatuses() {
            return retryableStatuses;
        }

        public void setRetryableStatuses(String[] retryableStatuses) {
            this.retryableStatuses = retryableStatuses;
        }

        public String[] getRetryableMethods() {
            return retryableMethods;
        }

        public void setRetryableMethods(String[] retryableMethods) {
            this.retryableMethods = retryableMethods;
        }
    }
}
