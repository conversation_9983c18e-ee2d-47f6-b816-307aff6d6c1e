package com.whiskerguard.gateway.config;

import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;

/**
 * 权限路径匹配器
 * 根据请求路径和HTTP方法匹配对应的功能权限编码
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
@Component
public class PermissionPathMatcher {

    private static final Logger log = LoggerFactory.getLogger(PermissionPathMatcher.class);

    private final AntPathMatcher pathMatcher = new AntPathMatcher();
    private final LicensePermissionProperties properties;

    public PermissionPathMatcher(LicensePermissionProperties properties) {
        this.properties = properties;
    }

    /**
     * 根据请求路径和方法获取对应的功能权限编码
     *
     * @param path   请求路径
     * @param method HTTP方法
     * @return 功能权限编码，如果不需要权限验证则返回null
     */
    public String getFeatureCode(String path, String method) {
        if (!properties.isEnabled()) {
            log.debug("License permission is disabled, skipping path matching");
            return null;
        }

        List<LicensePermissionProperties.PermissionPathMapping> mappings = properties.getPathMappings();
        if (mappings == null || mappings.isEmpty()) {
            log.debug("No permission path mappings configured");
            return null;
        }

        for (LicensePermissionProperties.PermissionPathMapping mapping : mappings) {
            if (matchesPath(mapping, path, method)) {
                log.debug("Path {} with method {} matched feature: {}", path, method, mapping.getFeature());
                return mapping.getFeature();
            }
        }

        log.debug("No permission mapping found for path: {} method: {}", path, method);
        return null;
    }

    /**
     * 检查路径和方法是否匹配指定的映射规则
     *
     * @param mapping 权限路径映射
     * @param path    请求路径
     * @param method  HTTP方法
     * @return 是否匹配
     */
    private boolean matchesPath(LicensePermissionProperties.PermissionPathMapping mapping, String path, String method) {
        // 检查路径匹配
        if (!pathMatcher.match(mapping.getPath(), path)) {
            return false;
        }

        // 检查HTTP方法匹配
        List<String> allowedMethods = mapping.getMethods();
        if (allowedMethods == null || allowedMethods.isEmpty()) {
            // 如果没有配置方法限制，则匹配所有方法
            return true;
        }

        // 检查是否包含通配符或具体方法
        return (
            allowedMethods.contains("*") || allowedMethods.contains(method.toUpperCase()) || allowedMethods.contains(method.toLowerCase())
        );
    }

    /**
     * 检查指定路径是否需要权限验证
     *
     * @param path   请求路径
     * @param method HTTP方法
     * @return 是否需要权限验证
     */
    public boolean requiresPermissionCheck(String path, String method) {
        return getFeatureCode(path, method) != null;
    }

    /**
     * 获取所有配置的权限路径映射
     *
     * @return 权限路径映射列表
     */
    public List<LicensePermissionProperties.PermissionPathMapping> getAllMappings() {
        return properties.getPathMappings();
    }
}
