package com.whiskerguard.gateway.monitoring;

import io.github.resilience4j.circuitbreaker.CircuitBreaker;
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import java.util.Collections;
import java.util.Map;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Configuration;

@Configuration
public class CircuitBreakerMetrics {

    private static final Logger log = LoggerFactory.getLogger(CircuitBreakerMetrics.class);

    private final CircuitBreakerRegistry circuitBreakerRegistry;
    private final MeterRegistry meterRegistry;

    public CircuitBreakerMetrics(CircuitBreakerRegistry circuitBreakerRegistry, MeterRegistry meterRegistry) {
        this.circuitBreakerRegistry = circuitBreakerRegistry;
        this.meterRegistry = meterRegistry;
    }

    @PostConstruct
    public void init() {
        // 注册所有熔断器的指标
        circuitBreakerRegistry.getAllCircuitBreakers().forEach(this::registerMetrics);

        // 添加新熔断器的监听器
        circuitBreakerRegistry.getEventPublisher().onEntryAdded(event -> registerMetrics(event.getAddedEntry()));
    }

    private void registerMetrics(CircuitBreaker circuitBreaker) {
        String name = circuitBreaker.getName();
        Iterable<Tag> tags = Collections.singletonList(Tag.of("name", name));

        // 注册熔断器状态指标
        meterRegistry.gauge("circuitbreaker.state", tags, circuitBreaker, cb -> {
            switch (cb.getState()) {
                case CLOSED:
                    return 0;
                case OPEN:
                    return 1;
                case HALF_OPEN:
                    return 2;
                default:
                    return -1;
            }
        });

        // 注册失败率指标
        meterRegistry.gauge("circuitbreaker.failure_rate", tags, circuitBreaker, cb -> cb.getMetrics().getFailureRate());

        // 注册慢调用率指标
        meterRegistry.gauge("circuitbreaker.slow_call_rate", tags, circuitBreaker, cb -> cb.getMetrics().getSlowCallRate());

        // 注册总调用次数指标
        meterRegistry.gauge(
            "circuitbreaker.total_calls",
            tags,
            circuitBreaker,
            cb ->
                cb.getMetrics().getNumberOfSuccessfulCalls() +
                cb.getMetrics().getNumberOfFailedCalls() +
                cb.getMetrics().getNumberOfNotPermittedCalls()
        );

        // 注册成功调用次数指标
        meterRegistry.gauge("circuitbreaker.successful_calls", tags, circuitBreaker, cb -> cb.getMetrics().getNumberOfSuccessfulCalls());

        // 注册失败调用次数指标
        meterRegistry.gauge("circuitbreaker.failed_calls", tags, circuitBreaker, cb -> cb.getMetrics().getNumberOfFailedCalls());

        // 注册被拒绝调用次数指标
        meterRegistry.gauge("circuitbreaker.not_permitted_calls", tags, circuitBreaker, cb -> cb.getMetrics().getNumberOfNotPermittedCalls()
        );

        log.info("Registered metrics for circuit breaker: {}", name);
    }
}
