package com.whiskerguard.gateway.monitoring;

import io.github.resilience4j.retry.Retry;
import io.github.resilience4j.retry.RetryRegistry;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Tag;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * Monitoring class for retry operations.
 * Collects and reports retry metrics.
 */
@Component
public class RetryMetrics {

    private final Logger log = LoggerFactory.getLogger(RetryMetrics.class);
    private final RetryRegistry retryRegistry;
    private final MeterRegistry meterRegistry;
    private final Map<String, Long> lastRetryCounts = new HashMap<>();

    public RetryMetrics(RetryRegistry retryRegistry, MeterRegistry meterRegistry) {
        this.retryRegistry = retryRegistry;
        this.meterRegistry = meterRegistry;
    }

    @Scheduled(fixedRate = 60000)
    public void collectRetryMetrics() {
        retryRegistry
            .getAllRetries()
            .forEach(retry -> {
                String name = retry.getName();
                Retry.Metrics metrics = retry.getMetrics();

                // Record retry attempts
                meterRegistry.gauge(
                    "retry.attempts",
                    Arrays.asList(Tag.of("service", name)),
                    metrics.getNumberOfSuccessfulCallsWithRetryAttempt()
                );

                // Record retry failures
                meterRegistry.gauge(
                    "retry.failures",
                    Arrays.asList(Tag.of("service", name)),
                    metrics.getNumberOfFailedCallsWithRetryAttempt()
                );

                // Calculate retry rate
                long currentRetryCount =
                    metrics.getNumberOfSuccessfulCallsWithRetryAttempt() + metrics.getNumberOfFailedCallsWithRetryAttempt();
                long lastRetryCount = lastRetryCounts.getOrDefault(name, 0L);
                long retryRate = currentRetryCount - lastRetryCount;
                lastRetryCounts.put(name, currentRetryCount);

                meterRegistry.gauge("retry.rate", Arrays.asList(Tag.of("service", name)), retryRate);

                // Log retry statistics
                log.info(
                    "Retry statistics for {}: attempts={}, failures={}, rate={}/min",
                    name,
                    metrics.getNumberOfSuccessfulCallsWithRetryAttempt(),
                    metrics.getNumberOfFailedCallsWithRetryAttempt(),
                    retryRate
                );
            });
    }
}
