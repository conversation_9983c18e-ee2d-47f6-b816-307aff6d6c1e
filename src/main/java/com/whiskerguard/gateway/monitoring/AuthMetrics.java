package com.whiskerguard.gateway.monitoring;

import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import org.springframework.stereotype.Component;

@Component
public class AuthMetrics {

    private final Counter loginSuccessCounter;
    private final Counter loginFailureCounter;

    public AuthMetrics(MeterRegistry registry) {
        this.loginSuccessCounter = Counter.builder("auth.login.success").description("Number of successful logins").register(registry);
        this.loginFailureCounter = Counter.builder("auth.login.failure").description("Number of failed logins").register(registry);
    }

    public void incrementLoginSuccess() {
        loginSuccessCounter.increment();
    }

    public void incrementLoginFailure() {
        loginFailureCounter.increment();
    }
}
