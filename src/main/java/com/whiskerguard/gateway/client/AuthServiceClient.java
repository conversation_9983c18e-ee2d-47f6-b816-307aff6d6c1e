package com.whiskerguard.gateway.client;

import com.whiskerguard.gateway.dto.UserAuthDTO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * 描述：认证服务客户端
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/28
 */
@ReactiveFeignClient(name = "whiskerguardauthservice", fallback = AuthServiceClientFallback.class)
public interface AuthServiceClient {

    /**
     * 验证用户权限
     *
     * @param username 用户名
     * @param method   请求方法
     * @return 是否有权限
     */
    @GetMapping("/api/auth/permission/verify")
    Mono<Boolean> verifyPermission(@RequestParam("username") String username,
                                   @RequestParam("method") String method);
    
    /**
     * 刷新用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    @GetMapping("/api/auth/user/refresh")
    Mono<UserAuthDTO> refreshUserInfo(@RequestParam Long userId);
}
