package com.whiskerguard.gateway.client;

import com.whiskerguard.gateway.dto.UserAuthDTO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 描述：认证服务降级处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/5/28
 */
@Component
public class AuthServiceClientFallback implements AuthServiceClient {

    private static final Logger LOG = LoggerFactory.getLogger(AuthServiceClientFallback.class);

    @Override
    public Mono<Boolean> verifyPermission(String username, String method) {
        LOG.warn("Auth service is unavailable, returning fallback response for permission verification");
        return Mono.just(Boolean.TRUE);
    }

    @Override
    public Mono<UserAuthDTO> refreshUserInfo(Long userId) {
        LOG.warn("Auth service is unavailable, returning fallback response for user info refresh");
        return Mono.empty();
    }
}
