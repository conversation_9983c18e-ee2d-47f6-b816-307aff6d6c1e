package com.whiskerguard.gateway.client;

import com.whiskerguard.gateway.dto.PermissionCheckRequest;
import com.whiskerguard.gateway.dto.PermissionCheckResult;
import com.whiskerguard.gateway.dto.TenantPermissionVO;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * License权限验证服务客户端
 * 用于调用License微服务的权限验证API
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
@ReactiveFeignClient(name = "whiskerguardlicenseservice", fallback = LicensePermissionClientFallback.class)
public interface LicensePermissionClient {
    /**
     * 简化版权限检查 - 推荐用于网关快速权限验证
     *
     * @param tenantId 租户ID
     * @param featureCode 功能权限编码
     * @return 是否有权限 (true=有权限，false=无权限)
     */
    @GetMapping("/api/license/permission/{tenantId}/simple/check")
    Mono<Boolean> hasPermission(@PathVariable("tenantId") String tenantId, @RequestParam("featureCode") String featureCode);

    /**
     * 详细版权限检查 - 需要详细权限信息时使用
     *
     * @param tenantId 租户ID
     * @param request 权限检查请求
     * @return 完整的权限检查结果
     */
    @PostMapping("/api/license/permission/{tenantId}/check")
    Mono<PermissionCheckResult> checkPermission(@PathVariable("tenantId") String tenantId, @RequestBody PermissionCheckRequest request);

    /**
     * 获取租户所有权限 - 用于网关缓存租户权限信息
     *
     * @param tenantId 租户ID
     * @return 租户的所有功能权限列表
     */
    @GetMapping("/api/license/permission/{tenantId}/permissions")
    Mono<List<TenantPermissionVO>> getTenantPermissions(@PathVariable("tenantId") String tenantId);
}
