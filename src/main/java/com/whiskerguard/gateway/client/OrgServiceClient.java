package com.whiskerguard.gateway.client;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import reactivefeign.spring.config.ReactiveFeignClient;
import reactor.core.publisher.Mono;

/**
 * 描述：组织服务客户端
 *
 * <AUTHOR>
 * @version 1.0
 * @date @date 2025/7/6
 */
@ReactiveFeignClient(name = "whiskerguardorgservice", fallback = OrgServiceClientFallback.class)
public interface OrgServiceClient {

    @GetMapping("/api/tenants/check")
    Mono<Boolean> checkTenantAccess(@RequestParam("tenantId") Long tenantId);

}
