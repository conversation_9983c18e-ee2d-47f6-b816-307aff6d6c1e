package com.whiskerguard.gateway.client;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;

/**
 * 描述：组织服务降级处理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/6
 */
@Component
public class OrgServiceClientFallback implements OrgServiceClient {

    private static final Logger LOG = LoggerFactory.getLogger(OrgServiceClientFallback.class);

    @Override
    public Mono<Boolean> checkTenantAccess(Long tenantId) {
        LOG.warn("Org service is unavailable, returning fallback response for tenant access check");
        return Mono.just(Boolean.TRUE);
    }
}
