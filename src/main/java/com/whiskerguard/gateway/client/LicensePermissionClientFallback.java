package com.whiskerguard.gateway.client;

import com.whiskerguard.gateway.dto.PermissionCheckRequest;
import com.whiskerguard.gateway.dto.PermissionCheckResult;
import com.whiskerguard.gateway.dto.TenantPermissionVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import reactor.core.publisher.Mono;

import java.util.Collections;
import java.util.List;

/**
 * License权限验证服务客户端降级处理
 * 当License服务不可用时提供降级响应
 * <p>
 * 注意：此类不应标注@Component，它只作为ReactiveFeignClient的fallback实现
 * ReactiveFeignClient会自动实例化此类作为降级处理器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
public class LicensePermissionClientFallback implements LicensePermissionClient {

    private static final Logger log = LoggerFactory.getLogger(LicensePermissionClientFallback.class);

    // 注意：由于这是fallback实例，无法直接注入依赖，需要通过其他方式获取配置
    // 这里我们使用系统属性或环境变量来获取fallback策略

    @Override
    public Mono<Boolean> hasPermission(String tenantId, String featureCode) {
        log.error(
            "🔥 CIRCUIT BREAKER ACTIVATED: License service is unavailable, using fallback for permission check: tenantId={}, featureCode={}",
            tenantId,
            featureCode
        );
        log.error("🔥 This indicates that the License service is down or experiencing issues");

        // 检查是否配置了fallback-allow策略
        boolean fallbackAllow = getFallbackAllowPolicy();

        if (fallbackAllow) {
            log.warn(
                "🔥 Fallback policy: ALLOW - Granting access due to fallback-allow=true for tenant: {}, feature: {}",
                tenantId,
                featureCode
            );
            return Mono.just(true);
        } else {
            log.error(
                "🔥 Fallback policy: DENY - Denying access due to fallback-allow=false for tenant: {}, feature: {}",
                tenantId,
                featureCode
            );
            return Mono.just(false);
        }
    }

    /**
     * 获取fallback允许策略
     * 由于fallback实例无法注入依赖，通过系统属性获取配置
     */
    private boolean getFallbackAllowPolicy() {
        // 首先尝试从系统属性获取
        String fallbackAllow = System.getProperty("license.permission.fallback-allow");
        if (fallbackAllow != null) {
            log.info("🔥 Found fallback-allow from system property: {}", fallbackAllow);
            return Boolean.parseBoolean(fallbackAllow);
        }

        // 然后尝试从环境变量获取
        fallbackAllow = System.getenv("LICENSE_PERMISSION_FALLBACK_ALLOW");
        if (fallbackAllow != null) {
            log.info("🔥 Found fallback-allow from environment variable: {}", fallbackAllow);
            return Boolean.parseBoolean(fallbackAllow);
        }

        // 检查Spring Profile，如果是dev环境，默认允许
        String activeProfiles = System.getProperty("spring.profiles.active");
        log.info("🔥 Active profiles: {}", activeProfiles);

        if (activeProfiles != null && activeProfiles.contains("dev")) {
            log.info("🔥 Detected dev profile, defaulting to fallback-allow=true");
            return true;
        }

        // 在开发环境下，如果没有明确配置，默认允许（临时解决方案）
        log.warn("🔥 No explicit fallback policy found, defaulting to ALLOW for development");
        return true; // 临时改为默认允许，便于开发调试
    }

    @Override
    public Mono<PermissionCheckResult> checkPermission(String tenantId, PermissionCheckRequest request) {
        log.warn(
            "License service is unavailable, using fallback for detailed permission check: tenantId={}, featureCode={}",
            tenantId,
            request != null ? request.getFeatureCode() : "null"
        );

        boolean fallbackAllow = getFallbackAllowPolicy();
        PermissionCheckResult result;

        if (fallbackAllow) {
            result = PermissionCheckResult.success(
                request != null ? request.getFeatureCode() : "UNKNOWN",
                "权限验证服务不可用，开发环境允许访问"
            );
        } else {
            result = PermissionCheckResult.failure(
                request != null ? request.getFeatureCode() : "UNKNOWN",
                "权限验证服务不可用，请稍后重试"
            );
        }

        return Mono.just(result);
    }

    @Override
    public Mono<List<TenantPermissionVO>> getTenantPermissions(String tenantId) {
        log.warn("License service is unavailable, returning empty permissions list for tenant: {}", tenantId);
        return Mono.just(Collections.emptyList());
    }
}
