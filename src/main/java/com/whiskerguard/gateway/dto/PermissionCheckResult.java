package com.whiskerguard.gateway.dto;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 权限检查结果DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
public class PermissionCheckResult implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 是否有权限
     */
    private boolean hasPermission;

    /**
     * 功能权限编码
     */
    private String featureCode;

    /**
     * 权限检查消息
     */
    private String message;

    /**
     * 错误码 (如果有错误)
     */
    private String errorCode;

    /**
     * 检查时间
     */
    private LocalDateTime checkTime;

    /**
     * 权限过期时间 (如果适用)
     */
    private LocalDateTime expiryTime;

    public PermissionCheckResult() {
        this.checkTime = LocalDateTime.now();
    }

    public PermissionCheckResult(boolean hasPermission, String featureCode, String message) {
        this();
        this.hasPermission = hasPermission;
        this.featureCode = featureCode;
        this.message = message;
    }

    /**
     * 创建成功的权限检查结果
     */
    public static PermissionCheckResult success(String featureCode, String message) {
        return new PermissionCheckResult(true, featureCode, message);
    }

    /**
     * 创建失败的权限检查结果
     */
    public static PermissionCheckResult failure(String featureCode, String message) {
        return new PermissionCheckResult(false, featureCode, message);
    }

    /**
     * 创建带错误码的失败结果
     */
    public static PermissionCheckResult failure(String featureCode, String message, String errorCode) {
        PermissionCheckResult result = new PermissionCheckResult(false, featureCode, message);
        result.setErrorCode(errorCode);
        return result;
    }

    public boolean isHasPermission() {
        return hasPermission;
    }

    public void setHasPermission(boolean hasPermission) {
        this.hasPermission = hasPermission;
    }

    public String getFeatureCode() {
        return featureCode;
    }

    public void setFeatureCode(String featureCode) {
        this.featureCode = featureCode;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public String getErrorCode() {
        return errorCode;
    }

    public void setErrorCode(String errorCode) {
        this.errorCode = errorCode;
    }

    public LocalDateTime getCheckTime() {
        return checkTime;
    }

    public void setCheckTime(LocalDateTime checkTime) {
        this.checkTime = checkTime;
    }

    public LocalDateTime getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(LocalDateTime expiryTime) {
        this.expiryTime = expiryTime;
    }

    @Override
    public String toString() {
        return (
            "PermissionCheckResult{" +
            "hasPermission=" +
            hasPermission +
            ", featureCode='" +
            featureCode +
            '\'' +
            ", message='" +
            message +
            '\'' +
            ", errorCode='" +
            errorCode +
            '\'' +
            ", checkTime=" +
            checkTime +
            ", expiryTime=" +
            expiryTime +
            '}'
        );
    }
}
