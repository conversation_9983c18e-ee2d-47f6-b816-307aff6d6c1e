package com.whiskerguard.gateway.dto;

import java.io.Serial;
import java.io.Serializable;

/**
 * 权限检查请求DTO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
public class PermissionCheckRequest implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 功能权限编码
     */
    private String featureCode;

    /**
     * 租户ID (可选，如果不提供则从请求头获取)
     */
    private String tenantId;

    /**
     * 用户ID (可选)
     */
    private String userId;

    /**
     * 额外的上下文信息 (可选)
     */
    private String context;

    public PermissionCheckRequest() {}

    public PermissionCheckRequest(String featureCode) {
        this.featureCode = featureCode;
    }

    public PermissionCheckRequest(String featureCode, String tenantId) {
        this.featureCode = featureCode;
        this.tenantId = tenantId;
    }

    public String getFeatureCode() {
        return featureCode;
    }

    public void setFeatureCode(String featureCode) {
        this.featureCode = featureCode;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getContext() {
        return context;
    }

    public void setContext(String context) {
        this.context = context;
    }

    @Override
    public String toString() {
        return (
            "PermissionCheckRequest{" +
            "featureCode='" +
            featureCode +
            '\'' +
            ", tenantId='" +
            tenantId +
            '\'' +
            ", userId='" +
            userId +
            '\'' +
            ", context='" +
            context +
            '\'' +
            '}'
        );
    }
}
