package com.whiskerguard.gateway.dto;

import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 租户权限信息VO
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
public class TenantPermissionVO implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * 租户ID
     */
    private String tenantId;

    /**
     * 功能权限编码
     */
    private String featureCode;

    /**
     * 功能名称
     */
    private String featureName;

    /**
     * 是否有权限
     */
    private boolean hasPermission;

    /**
     * 权限过期时间
     */
    private LocalDateTime expiryTime;

    /**
     * 套餐类型
     */
    private String packageType;

    /**
     * 权限描述
     */
    private String description;

    public TenantPermissionVO() {}

    public TenantPermissionVO(String tenantId, String featureCode, boolean hasPermission) {
        this.tenantId = tenantId;
        this.featureCode = featureCode;
        this.hasPermission = hasPermission;
    }

    public String getTenantId() {
        return tenantId;
    }

    public void setTenantId(String tenantId) {
        this.tenantId = tenantId;
    }

    public String getFeatureCode() {
        return featureCode;
    }

    public void setFeatureCode(String featureCode) {
        this.featureCode = featureCode;
    }

    public String getFeatureName() {
        return featureName;
    }

    public void setFeatureName(String featureName) {
        this.featureName = featureName;
    }

    public boolean isHasPermission() {
        return hasPermission;
    }

    public void setHasPermission(boolean hasPermission) {
        this.hasPermission = hasPermission;
    }

    public LocalDateTime getExpiryTime() {
        return expiryTime;
    }

    public void setExpiryTime(LocalDateTime expiryTime) {
        this.expiryTime = expiryTime;
    }

    public String getPackageType() {
        return packageType;
    }

    public void setPackageType(String packageType) {
        this.packageType = packageType;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return (
            "TenantPermissionVO{" +
            "tenantId='" +
            tenantId +
            '\'' +
            ", featureCode='" +
            featureCode +
            '\'' +
            ", featureName='" +
            featureName +
            '\'' +
            ", hasPermission=" +
            hasPermission +
            ", expiryTime=" +
            expiryTime +
            ", packageType='" +
            packageType +
            '\'' +
            ", description='" +
            description +
            '\'' +
            '}'
        );
    }
}
