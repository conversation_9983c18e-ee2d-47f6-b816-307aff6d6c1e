package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.cache.PermissionCacheManager;
import com.whiskerguard.gateway.client.LicensePermissionClient;
import com.whiskerguard.gateway.config.LicensePermissionProperties;
import com.whiskerguard.gateway.config.PermissionPathMatcher;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

/**
 * License权限验证过滤器
 * 在网关层统一拦截需要权限验证的请求
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025-07-10
 */
@Component
public class LicensePermissionFilter implements GlobalFilter, Ordered, ApplicationContextAware {

    private static final Logger log = LoggerFactory.getLogger(LicensePermissionFilter.class);

    private static final String TENANT_ID_HEADER = "X-TENANT-ID";
    private static final String USER_ID_HEADER = "X-USER-ID";

    private ApplicationContext applicationContext;
    private LicensePermissionClient licenseClient;
    private final PermissionPathMatcher pathMatcher;
    private final PermissionCacheManager cacheManager;
    private final LicensePermissionProperties properties;

    public LicensePermissionFilter(
        PermissionPathMatcher pathMatcher,
        PermissionCacheManager cacheManager,
        LicensePermissionProperties properties
    ) {
        this.pathMatcher = pathMatcher;
        this.cacheManager = cacheManager;
        this.properties = properties;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        this.applicationContext = applicationContext;
    }

    /**
     * 延迟获取 LicensePermissionClient 以避免循环依赖
     */
    private LicensePermissionClient getLicenseClient() {
        if (licenseClient == null) {
            licenseClient = applicationContext.getBean(LicensePermissionClient.class);
        }
        return licenseClient;
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        // 检查权限验证是否启用
        if (!properties.isEnabled()) {
            log.debug("License permission verification is disabled, skipping");
            return chain.filter(exchange);
        }

        String path = exchange.getRequest().getPath().value();
        String method = exchange.getRequest().getMethod().name();

        log.debug("Processing request: {} {}", method, path);

        // 1. 检查是否需要权限验证
        String featureCode = pathMatcher.getFeatureCode(path, method);
        if (featureCode == null) {
            log.debug("No permission required for path: {} {}", method, path);
            return chain.filter(exchange); // 不需要权限验证，直接放行
        }

        log.info("Permission check required for path: {} {}, feature: {}", method, path, featureCode);

        // 2. 提取租户ID
        String tenantId = getTenantId(exchange);
        if (!StringUtils.hasText(tenantId)) {
            log.warn("Missing tenant ID for permission check: {} {}", method, path);
            return handleUnauthorized(exchange, "缺少租户ID");
        }

        // 3. 检查权限（先查缓存，再调用服务）
        return checkPermission(tenantId, featureCode)
            .flatMap(hasPermission -> {
                if (hasPermission) {
                    log.info("Permission granted for tenant: {}, feature: {}, path: {} {}", tenantId, featureCode, method, path);
                    return chain.filter(exchange);
                } else {
                    log.warn("Permission denied for tenant: {}, feature: {}, path: {} {}", tenantId, featureCode, method, path);
                    return handleForbidden(exchange, "权限不足：缺少功能权限 " + featureCode);
                }
            })
            .onErrorResume(throwable -> {
                log.error(
                    "Error during permission check for tenant: {}, feature: {}, path: {} {}, error: {}",
                    tenantId,
                    featureCode,
                    method,
                    path,
                    throwable.getMessage()
                );
                return handlePermissionCheckError(exchange, chain, throwable);
            });
    }

    /**
     * 检查权限（先查缓存，再调用服务）
     */
    private Mono<Boolean> checkPermission(String tenantId, String featureCode) {
        log.debug("Starting permission check for tenant: {}, feature: {}", tenantId, featureCode);

        // 先查缓存
        return cacheManager
            .getPermission(tenantId, featureCode)
            .doOnNext(cachedResult ->
                log.info("Using cached permission result for tenant: {}, feature: {}, result: {}", tenantId, featureCode, cachedResult)
            )
            .switchIfEmpty(
                // 缓存未命中，调用License服务
                Mono.defer(() -> {
                    log.info("Cache miss for tenant: {}, feature: {}, calling License service", tenantId, featureCode);
                    return callLicenseService(tenantId, featureCode)
                        .doOnNext(result -> {
                            log.info(
                                "License service returned result for tenant: {}, feature: {}, result: {}",
                                tenantId,
                                featureCode,
                                result
                            );
                            // 缓存结果
                            cacheManager
                                .cachePermission(tenantId, featureCode, result)
                                .subscribe(
                                    cached ->
                                        log.info(
                                            "Permission result cached for tenant: {}, feature: {}, result: {}",
                                            tenantId,
                                            featureCode,
                                            result
                                        ),
                                    error ->
                                        log.warn(
                                            "Failed to cache permission result for tenant: {}, feature: {}, error: {}",
                                            tenantId,
                                            featureCode,
                                            error.getMessage()
                                        )
                                );
                        })
                        .doOnError(error ->
                            log.error(
                                "License service call failed for tenant: {}, feature: {}, error: {}",
                                tenantId,
                                featureCode,
                                error.getMessage()
                            )
                        );
                })
            )
            .doOnNext(result ->
                log.info("Final permission check result for tenant: {}, feature: {}, result: {}", tenantId, featureCode, result)
            );
    }

    /**
     * 调用License服务进行权限验证
     */
    private Mono<Boolean> callLicenseService(String tenantId, String featureCode) {
        log.info("🚀 Starting License service call: tenant={}, feature={}, time={}", tenantId, featureCode, System.currentTimeMillis());

        long startTime = System.currentTimeMillis();

        return getLicenseClient()
            .hasPermission(tenantId, featureCode)
            .doOnNext(result -> {
                long duration = System.currentTimeMillis() - startTime;
                log.info(
                    "✅ License service response: tenant={}, feature={}, result={}, duration={}ms",
                    tenantId,
                    featureCode,
                    result,
                    duration
                );
            })
            .doOnError(error -> {
                long duration = System.currentTimeMillis() - startTime;
                log.error(
                    "❌ License service call failed: tenant={}, feature={}, error={}, duration={}ms",
                    tenantId,
                    featureCode,
                    error.getMessage(),
                    duration
                );

                // 检查是否是超时错误
                if (error.getMessage() != null && error.getMessage().toLowerCase().contains("timeout")) {
                    log.error("⏰ TIMEOUT DETECTED: License service call timed out after {}ms", duration);
                }
            });
    }

    /**
     * 提取租户ID
     */
    private String getTenantId(ServerWebExchange exchange) {
        return exchange.getRequest().getHeaders().getFirst(TENANT_ID_HEADER);
    }

    /**
     * 处理未授权访问
     */
    private Mono<Void> handleUnauthorized(ServerWebExchange exchange, String message) {
        log.warn("Unauthorized access: {}", message);
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String body = String.format("{\"error\":\"Unauthorized\",\"message\":\"%s\",\"title\":\"%s\"}", message, message);
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }

    /**
     * 处理权限不足
     */
    private Mono<Void> handleForbidden(ServerWebExchange exchange, String message) {
        log.warn("Access forbidden: {}", message);
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.INTERNAL_SERVER_ERROR);
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        String body = String.format("{\"error\":\"Forbidden\",\"message\":\"%s\",\"title\":\"%s\"}", message, message);
        return response.writeWith(Mono.just(response.bufferFactory().wrap(body.getBytes())));
    }

    /**
     * 处理权限验证错误（降级策略）
     */
    private Mono<Void> handlePermissionCheckError(ServerWebExchange exchange, GatewayFilterChain chain, Throwable throwable) {
        log.error("Permission check error, applying fallback strategy: {}", throwable.getMessage());

        // 根据配置决定是否允许访问
        if (properties.isFallbackAllow()) {
            log.warn("License service unavailable, allowing access due to fallback policy");
            return chain.filter(exchange);
        } else {
            log.warn("License service unavailable, denying access due to fallback policy");
            return handleForbidden(exchange, "权限验证服务不可用，请稍后重试");
        }
    }

    @Override
    public int getOrder() {
        // 设置较高优先级，在用户信息注入之后，路由之前执行
        return 100;
    }
}
