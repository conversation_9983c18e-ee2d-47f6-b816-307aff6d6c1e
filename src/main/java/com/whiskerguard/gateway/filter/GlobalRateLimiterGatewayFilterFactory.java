package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.config.RateLimiterConfiguration;
import io.github.bucket4j.Bucket;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

import java.time.Duration;
import java.time.Instant;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Spring Cloud Gateway 全局限流过滤器工厂增强版。
 * <p>
 * 主要功能特性：
 * - 基于Redis的限流机制，支持Bucket4j本地降级
 * - 支持多种限流算法（令牌桶、滑动窗口）
 * - 可配置的重试机制，支持指数退避
 * - 全面的监控和指标收集
 * - 动态配置支持，支持热重载
 * - 线程安全实现，合理的资源管理
 * - 详细的错误处理和日志记录
 * - 支持不同粒度的限流（全局、IP、用户、租户）
 * <p>
 * 配置选项说明：
 * - replenishRate: 每秒向桶中添加的令牌数
 * - burstCapacity: 桶的最大令牌容量
 * - timeWindow: 限流时间窗口（秒）
 * - algorithm: 限流算法类型（TOKEN_BUCKET、SLIDING_WINDOW）
 * - enableRetry: 是否启用失败请求重试机制
 * - maxRetryAttempts: 最大重试次数
 * - retryBackoffMultiplier: 重试延迟的退避倍数
 * - enableLocalFallback: 当Redis不可用时是否启用本地降级
 *
 * <AUTHOR> Team
 * @version 2.0
 * @since 1.0
 */
@Component
public class GlobalRateLimiterGatewayFilterFactory extends AbstractGatewayFilterFactory<GlobalRateLimiterGatewayFilterFactory.Config> {

    private static final Logger log = LoggerFactory.getLogger(GlobalRateLimiterGatewayFilterFactory.class);

    // 核心组件
    private final RedisRateLimiter rateLimiter;
    private final KeyResolver keyResolver;
    private final MeterRegistry meterRegistry;
    private final RateLimiterConfiguration rateLimiterConfiguration;

    // 指标监控组件
    private final Map<String, Counter> rateLimitCounters = new ConcurrentHashMap<>();
    private final Map<String, Timer> rateLimitTimers = new ConcurrentHashMap<>();
    private final Counter redisFailureCounter;
    private final Counter localFallbackCounter;

    // 常量定义
    private static final String RATE_LIMIT_HEADER_REMAINING = "X-RateLimit-Remaining";
    private static final String RATE_LIMIT_HEADER_LIMIT = "X-RateLimit-Limit";
    private static final String RATE_LIMIT_HEADER_RESET = "X-RateLimit-Reset";
    private static final String RATE_LIMIT_HEADER_RETRY_AFTER = "Retry-After";
    private static final int DEFAULT_RETRY_ATTEMPTS = 3;
    private static final Duration DEFAULT_RETRY_DELAY = Duration.ofMillis(100);

    public GlobalRateLimiterGatewayFilterFactory(
        @Qualifier("globalRateLimiter") RedisRateLimiter rateLimiter,
        @Qualifier("ipKeyResolver") KeyResolver keyResolver,
        MeterRegistry meterRegistry,
        RateLimiterConfiguration rateLimiterConfiguration) {
        super(Config.class);
        this.rateLimiter = rateLimiter;
        this.keyResolver = keyResolver;
        this.meterRegistry = meterRegistry;
        this.rateLimiterConfiguration = rateLimiterConfiguration;

        // 初始化全局指标
        this.redisFailureCounter = Counter.builder("rate_limiter_redis_failures_total")
            .description("限流器中Redis故障总次数")
            .register(meterRegistry);
        this.localFallbackCounter = Counter.builder("rate_limiter_local_fallback_total")
            .description("本地降级激活总次数")
            .register(meterRegistry);

        log.info("GlobalRateLimiterGatewayFilterFactory 已初始化，支持Redis降级机制");
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            Timer.Sample sample = Timer.start(meterRegistry);

            return keyResolver.resolve(exchange)
                .flatMap(key -> {
                    if (key == null) {
                        log.debug("未解析到限流键，允许请求通过");
                        return chain.filter(exchange);
                    }

                    log.debug("正在处理限流检查，键: {}, 算法: {}", key, config.getAlgorithm());

                    // 根据配置决定是否启用重试机制进行限流检查
                    Mono<Boolean> rateLimitCheck = config.isEnableRetry()
                        ? performRateLimitWithRetry(key, config)
                        : performRateLimit(key, config);

                    return rateLimitCheck.flatMap(allowed -> {
                        sample.stop(getTimer(key, allowed ? "allowed" : "limited"));

                        if (allowed) {
                            // 记录成功请求
                            getCounter(key, "allowed").increment();
                            log.debug("限流检查通过，键: {}", key);
                            return chain.filter(exchange);
                        } else {
                            // 记录被限流的请求
                            getCounter(key, "limited").increment();
                            log.warn("限流超出，键: {}, 算法: {}, 限制: {}/{}秒",
                                key, config.getAlgorithm(), config.getReplenishRate(), config.getTimeWindow());

                            return handleRateLimitExceeded(exchange, config, key);
                        }
                    });
                })
                .onErrorResume(throwable -> {
                    sample.stop(getTimer("error", "error"));
                    log.error("限流处理过程中发生错误", throwable);

                    // 发生错误时允许请求继续（失败开放策略）
                    getCounter("error", "fallback").increment();
                    return chain.filter(exchange);
                });
        };
    }

    /**
     * 执行带重试机制的限流检查（如果启用）。
     *
     * @param key    限流键
     * @param config 配置信息
     * @return Mono<Boolean> 表示请求是否被允许
     */
    private Mono<Boolean> performRateLimitWithRetry(String key, Config config) {
        return performRateLimit(key, config)
            .retryWhen(Retry.backoff(config.getMaxRetryAttempts(), DEFAULT_RETRY_DELAY)
                .multiplier(config.getRetryBackoffMultiplier())
                .maxBackoff(Duration.ofSeconds(5))
                .filter(throwable -> {
                    log.debug("由于以下原因重试限流检查: {}", throwable.getMessage());
                    return true;
                })
                .onRetryExhaustedThrow((retryBackoffSpec, retrySignal) -> {
                    log.warn("限流重试已耗尽，键: {}, 尝试次数: {}",
                        key, retrySignal.totalRetries());
                    return retrySignal.failure();
                }));
    }

    /**
     * 使用Redis执行实际的限流检查，支持本地降级。
     *
     * @param key    限流键
     * @param config 配置信息
     * @return Mono<Boolean> 表示请求是否被允许
     */
    private Mono<Boolean> performRateLimit(String key, Config config) {
        // Try Redis rate limiting first
        return rateLimiter.isAllowed(key, String.valueOf(config.getReplenishRate()))
            .map(response -> {
                log.debug("Redis限流响应，键 {}: 允许={}",
                    key, response.isAllowed());
                return response.isAllowed();
            })
            .onErrorResume(throwable -> {
                log.warn("Redis限流器失败，键: {}, 降级到本地限流。错误: {}",
                    key, throwable.getMessage());
                redisFailureCounter.increment();

                if (config.isEnableLocalFallback()) {
                    return performLocalRateLimit(key, config);
                } else {
                    // 如果本地降级被禁用，允许请求通过（失败开放）
                    log.warn("本地降级已禁用，允许请求通过，键: {}", key);
                    return Mono.just(true);
                }
            });
    }

    /**
     * 当Redis不可用时，使用Bucket4j执行本地限流。
     *
     * @param key    限流键
     * @param config 配置信息
     * @return Mono<Boolean> 表示请求是否被允许
     */
    private Mono<Boolean> performLocalRateLimit(String key, Config config) {
        localFallbackCounter.increment();

        try {
            Duration refillDuration = Duration.ofSeconds(config.getTimeWindow());
            Bucket bucket = rateLimiterConfiguration.getLocalBucket(
                key,
                config.getBurstCapacity(),
                config.getReplenishRate(),
                refillDuration
            );

            boolean allowed = bucket.tryConsume(1);
            log.debug("本地限流检查，键 {}: 允许={}, 可用令牌={}",
                key, allowed, bucket.getAvailableTokens());

            return Mono.just(allowed);
        } catch (Exception e) {
            log.error("本地限流失败，键: {}, 允许请求通过", key, e);
            return Mono.just(true); // 失败开放策略
        }
    }

    /**
     * 处理限流超出场景，设置适当的响应头和状态码。
     *
     * @param exchange 服务器Web交换对象
     * @param config   配置信息
     * @param key      限流键
     * @return Mono<Void> 表示完成状态
     */
    private Mono<Void> handleRateLimitExceeded(org.springframework.web.server.ServerWebExchange exchange, Config config, String key) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);

        // 计算重置时间
        long resetTime = Instant.now().plusSeconds(config.getTimeWindow()).getEpochSecond();

        // 添加完整的限流响应头
        response.getHeaders().add(RATE_LIMIT_HEADER_REMAINING, "0");
        response.getHeaders().add(RATE_LIMIT_HEADER_LIMIT, String.valueOf(config.getBurstCapacity()));
        response.getHeaders().add(RATE_LIMIT_HEADER_RESET, String.valueOf(resetTime));
        response.getHeaders().add(RATE_LIMIT_HEADER_RETRY_AFTER, String.valueOf(config.getTimeWindow()));

        // 添加自定义调试头
        response.getHeaders().add("X-RateLimit-Algorithm", config.getAlgorithm().toString());
        response.getHeaders().add("X-RateLimit-Key", key);

        log.info("限流超出，键: {}, 返回429请求过多状态码", key);

        return response.setComplete();
    }

    /**
     * 获取或创建指定键和状态的计数器。
     * 实现适当的清理机制以防止内存泄漏。
     *
     * @param key    限流键
     * @param status 状态（允许、限制、错误等）
     * @return Counter实例
     */
    private Counter getCounter(String key, String status) {
        String counterName = "rate_limiter_" + status + "_total";
        return rateLimitCounters.computeIfAbsent(counterName, k ->
            Counter.builder(counterName)
                .tag("key", sanitizeKey(key))
                .tag("status", status)
                .description("Rate limiter " + status + " requests")
                .register(meterRegistry)
        );
    }

    /**
     * 获取或创建指定键和状态的计时器。
     *
     * @param key    限流键
     * @param status 状态
     * @return Timer实例
     */
    private Timer getTimer(String key, String status) {
        String timerName = "rate_limiter_duration";
        return rateLimitTimers.computeIfAbsent(timerName + "_" + status, k ->
            Timer.builder(timerName)
                .tag("key", sanitizeKey(key))
                .tag("status", status)
                .description("Rate limiter processing duration")
                .register(meterRegistry)
        );
    }

    /**
     * 清理键值以用于指标收集，防止基数爆炸。
     *
     * @param key 原始键值
     * @return 清理后的键值
     */
    private String sanitizeKey(String key) {
        if (key == null) return "unknown";

        // 限制键长度并移除特殊字符
        String sanitized = key.replaceAll("[^a-zA-Z0-9._-]", "_");
        return sanitized.length() > 50 ? sanitized.substring(0, 50) : sanitized;
    }

    /**
     * 全局限流器的增强配置类。
     * 支持多种限流算法和高级功能特性。
     */
    public static class Config {

        /**
         * 限流算法类型枚举。
         */
        public enum Algorithm {
            /**
             * 令牌桶算法
             */
            TOKEN_BUCKET,
            /**
             * 滑动窗口算法
             */
            SLIDING_WINDOW,
            /**
             * 固定窗口算法
             */
            FIXED_WINDOW
        }

        // 核心限流参数
        private int replenishRate = 100;        // 令牌补充速率（每秒）
        private int burstCapacity = 100;        // 桶的最大容量
        private int timeWindow = 1;             // 时间窗口（秒）
        private Algorithm algorithm = Algorithm.TOKEN_BUCKET;  // 限流算法

        // 重试机制配置
        private boolean enableRetry = true;                    // 是否启用重试
        private int maxRetryAttempts = DEFAULT_RETRY_ATTEMPTS; // 最大重试次数
        private double retryBackoffMultiplier = 2.0;           // 重试退避倍数

        // 降级配置
        private boolean enableLocalFallback = true;            // 是否启用本地降级

        // 高级配置
        private boolean enableDetailedLogging = false;         // 是否启用详细日志
        private String customErrorMessage = "请求频率过高，请稍后再试。"; // 自定义错误消息

        // 获取器和设置器方法
        public int getReplenishRate() {
            return replenishRate;
        }

        public void setReplenishRate(int replenishRate) {
            this.replenishRate = Math.max(1, replenishRate);
        }

        public int getBurstCapacity() {
            return burstCapacity;
        }

        public void setBurstCapacity(int burstCapacity) {
            this.burstCapacity = Math.max(1, burstCapacity);
        }

        public int getTimeWindow() {
            return timeWindow;
        }

        public void setTimeWindow(int timeWindow) {
            this.timeWindow = Math.max(1, timeWindow);
        }

        public Algorithm getAlgorithm() {
            return algorithm;
        }

        public void setAlgorithm(Algorithm algorithm) {
            this.algorithm = algorithm != null ? algorithm : Algorithm.TOKEN_BUCKET;
        }

        public boolean isEnableRetry() {
            return enableRetry;
        }

        public void setEnableRetry(boolean enableRetry) {
            this.enableRetry = enableRetry;
        }

        public int getMaxRetryAttempts() {
            return maxRetryAttempts;
        }

        public void setMaxRetryAttempts(int maxRetryAttempts) {
            this.maxRetryAttempts = Math.max(0, Math.min(10, maxRetryAttempts));
        }

        public double getRetryBackoffMultiplier() {
            return retryBackoffMultiplier;
        }

        public void setRetryBackoffMultiplier(double retryBackoffMultiplier) {
            this.retryBackoffMultiplier = Math.max(1.0, Math.min(10.0, retryBackoffMultiplier));
        }

        public boolean isEnableLocalFallback() {
            return enableLocalFallback;
        }

        public void setEnableLocalFallback(boolean enableLocalFallback) {
            this.enableLocalFallback = enableLocalFallback;
        }

        public boolean isEnableDetailedLogging() {
            return enableDetailedLogging;
        }

        public void setEnableDetailedLogging(boolean enableDetailedLogging) {
            this.enableDetailedLogging = enableDetailedLogging;
        }

        public String getCustomErrorMessage() {
            return customErrorMessage;
        }

        public void setCustomErrorMessage(String customErrorMessage) {
            this.customErrorMessage = customErrorMessage != null ? customErrorMessage : "Rate limit exceeded";
        }

        @Override
        public String toString() {
            return "Config{" +
                "replenishRate=" + replenishRate +
                ", burstCapacity=" + burstCapacity +
                ", timeWindow=" + timeWindow +
                ", algorithm=" + algorithm +
                ", enableRetry=" + enableRetry +
                ", maxRetryAttempts=" + maxRetryAttempts +
                ", retryBackoffMultiplier=" + retryBackoffMultiplier +
                ", enableLocalFallback=" + enableLocalFallback +
                ", enableDetailedLogging=" + enableDetailedLogging +
                '}';
        }
    }
}
