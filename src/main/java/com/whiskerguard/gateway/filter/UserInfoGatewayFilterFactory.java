package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.client.AuthServiceClient;
import com.whiskerguard.gateway.client.OrgServiceClient;
import com.whiskerguard.gateway.config.ConsulRouteProperties;
import com.whiskerguard.gateway.dto.UserAuthDTO;
import com.whiskerguard.gateway.util.UserAuthDTOConverter;
import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Timer;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.security.authentication.AnonymousAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.ReactiveSecurityContextHolder;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import reactor.util.retry.Retry;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.Collections;
import java.util.List;
import java.util.UUID;

/**
 * 用户信息网关过滤器工厂
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2025/7/10
 */
@Component
public class UserInfoGatewayFilterFactory extends AbstractGatewayFilterFactory<UserInfoGatewayFilterFactory.Config> {

    private static final Logger log = LoggerFactory.getLogger(UserInfoGatewayFilterFactory.class);

    // 核心依赖组件
    private final StringRedisTemplate redisTemplate;
    private final AuthServiceClient authServiceClient;
    private final UserAuthDTOConverter converter;
    private final ConsulRouteProperties consulRouteProperties;
    private final OrgServiceClient orgServiceClient;
    private final MeterRegistry meterRegistry;

    // 常量定义
    private static final String BEARER_PREFIX = "Bearer ";
    private static final String TOKEN_BLACKLIST_PREFIX = "token_blacklist:";
    private static final String USER_INFO_PREFIX = "user_info:";
    private static final String HEADER_REQUEST_ID = "X-Request-ID";
    private static final String HEADER_USER_NAME = "X-USER-NAME";
    private static final String HEADER_USER_ID = "X-USER-ID";
    private static final String HEADER_TENANT_ID = "X-TENANT-ID";
    private static final String ANONYMOUS_USER = "anonymous";
    private static final String ROLE_ANONYMOUS = "ROLE_ANONYMOUS";
    private static final String SESSION_EXPIRED_MESSAGE = "会话已过期，请重新登录";
    private static final String PERMISSION_DENIED_MESSAGE = "权限不足，访问被拒绝";
    private static final String TENANT_ACCESS_DENIED_MESSAGE = "租户访问权限不足";

    public UserInfoGatewayFilterFactory(
        @Autowired StringRedisTemplate redisTemplate,
        @Lazy AuthServiceClient authServiceClient,
        UserAuthDTOConverter converter,
        ConsulRouteProperties consulRouteProperties,
        @Lazy OrgServiceClient orgServiceClient,
        MeterRegistry meterRegistry
    ) {
        super(Config.class);
        this.redisTemplate = redisTemplate;
        this.authServiceClient = authServiceClient;
        this.converter = converter;
        this.consulRouteProperties = consulRouteProperties;
        this.orgServiceClient = orgServiceClient;
        this.meterRegistry = meterRegistry;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            Timer.Sample sample = Timer.start(meterRegistry);

            return ReactiveSecurityContextHolder.getContext()
                .map(SecurityContext::getAuthentication)
                .switchIfEmpty(
                    Mono.just(
                        new AnonymousAuthenticationToken(
                            ANONYMOUS_USER,
                            ANONYMOUS_USER,
                            Collections.singletonList(new SimpleGrantedAuthority(ROLE_ANONYMOUS))
                        )
                    )
                )
                .publishOn(Schedulers.boundedElastic())
                .flatMap(authentication -> {
                    ServerHttpRequest request = exchange.getRequest();

                    // 检查是否跳过认证
                    if (shouldSkipAuthentication(request)) {
                        sample.stop(Timer.builder("user_info_processing_duration")
                            .tag("result", "skipped")
                            .register(meterRegistry));
                        log.debug("跳过认证检查，路径: {}", request.getURI().getPath());
                        return chain.filter(exchange);
                    }

                    HttpMethod method = request.getMethod();

                    // 检查Token黑名单
                    return checkTokenBlacklist(exchange)
                        .flatMap(isBlacklisted -> {
                            if (isBlacklisted) {
                                sample.stop(Timer.builder("user_info_processing_duration")
                                    .tag("result", "blacklisted")
                                    .register(meterRegistry));
                                return handleTokenBlacklisted(exchange);
                            }

                            // 添加请求追踪ID
                            ServerHttpRequest.Builder mutate = request.mutate();
                            String requestId = generateRequestId();
                            mutate.header(HEADER_REQUEST_ID, requestId);

                            if (authentication.isAuthenticated() && !isAnonymous(authentication)) {
                                return processAuthenticatedUser(exchange, chain, authentication, mutate, sample, method);
                            } else {
                                sample.stop(Timer.builder("user_info_processing_duration")
                                    .tag("result", "anonymous")
                                    .register(meterRegistry));
                                log.debug("匿名用户访问: {}", request.getURI().getPath());
                                return chain.filter(exchange.mutate().request(mutate.build()).build());
                            }
                        });
                })
                .onErrorResume(throwable -> {
                    sample.stop(Timer.builder("user_info_processing_duration")
                        .tag("result", "error")
                        .register(meterRegistry));
                    log.error("用户信息处理过程中发生错误", throwable);
                    return handleProcessingError(exchange, throwable);
                });
        };
    }

    /**
     * 检查是否应该跳过认证。
     *
     * @param request HTTP请求
     * @return 是否跳过认证
     */
    private boolean shouldSkipAuthentication(ServerHttpRequest request) {
        List<String> skipUrls = consulRouteProperties.getSkipUrls();
        if (CollectionUtils.isEmpty(skipUrls)) {
            return false;
        }

        String requestPath = request.getURI().getPath();
        return skipUrls.stream().anyMatch(requestPath::contains);
    }

    /**
     * 检查Token是否在黑名单中。
     *
     * @param exchange 服务器Web交换对象
     * @return Mono<Boolean> 是否在黑名单中
     */
    private Mono<Boolean> checkTokenBlacklist(org.springframework.web.server.ServerWebExchange exchange) {
        String token = extractToken(exchange.getRequest());
        if (!StringUtils.hasText(token)) {
            return Mono.just(false);
        }

        return Mono.fromCallable(() -> {
                String blacklistKey = TOKEN_BLACKLIST_PREFIX + token;
                Boolean isBlacklisted = redisTemplate.hasKey(blacklistKey);
                log.debug("Token黑名单检查: token={}, blacklisted={}",
                    token.substring(0, Math.min(token.length(), 10)) + "...", isBlacklisted);
                return isBlacklisted;
            })
            .subscribeOn(Schedulers.boundedElastic())
            .onErrorReturn(false); // Redis错误时默认不拦截
    }

    /**
     * 提取Authorization头中的Token。
     *
     * @param request HTTP请求
     * @return Token字符串，如果没有则返回null
     */
    private String extractToken(ServerHttpRequest request) {
        String authHeader = request.getHeaders().getFirst("Authorization");
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(BEARER_PREFIX)) {
            return authHeader.substring(BEARER_PREFIX.length());
        }
        return null;
    }

    /**
     * 生成请求追踪ID。
     *
     * @return 唯一的请求ID
     */
    private String generateRequestId() {
        return UUID.randomUUID().toString();
    }

    /**
     * 检查是否为匿名用户。
     *
     * @param authentication 认证对象
     * @return 是否为匿名用户
     */
    private boolean isAnonymous(org.springframework.security.core.Authentication authentication) {
        return authentication instanceof AnonymousAuthenticationToken;
    }

    /**
     * 处理已认证用户的请求。
     *
     * @param exchange       服务器Web交换对象
     * @param chain          过滤器链
     * @param authentication 认证对象
     * @param mutate         请求构建器
     * @param sample         计时器样本
     * @param method         HTTP方法
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> processAuthenticatedUser(
        ServerWebExchange exchange,
        GatewayFilterChain chain,
        Authentication authentication,
        ServerHttpRequest.Builder mutate,
        Timer.Sample sample,
        HttpMethod method) {
        ServerHttpRequest request = exchange.getRequest();
        String username = authentication.getName();

        log.info("已认证用户 {} 访问 {} {}", username, request.getMethod(), request.getURI());

        // 尝试从Redis获取用户信息
        return getUserInfoFromCache(username)
            .switchIfEmpty(getUserInfoFromService(username))
            .flatMap(userAuthDTO -> {
                if (userAuthDTO == null) {
                    sample.stop(Timer.builder("user_info_processing_duration")
                        .tag("result", "user_not_found")
                        .register(meterRegistry));
                    log.error("无法获取用户信息: {}", username);
                    return handleUserNotFound(exchange);
                }

                // 注入用户信息到请求头
                injectUserHeaders(mutate, userAuthDTO);

                // 执行权限检查
                return performAccessControl(exchange, chain, userAuthDTO, mutate, sample, method);
            })
            .onErrorResume(throwable -> {
                sample.stop(Timer.builder("user_info_processing_duration")
                    .tag("result", "error")
                    .register(meterRegistry));
                log.error("处理已认证用户时发生错误: {}", username, throwable);
                return handleProcessingError(exchange, throwable);
            });
    }

    /**
     * 从Redis缓存获取用户信息。
     *
     * @param username 用户名
     * @return Mono<UserAuthDTO> 用户信息
     */
    private Mono<UserAuthDTO> getUserInfoFromCache(String username) {
        return Mono.fromCallable(() -> {
                String userInfoKey = USER_INFO_PREFIX + username;
                String userInfo = redisTemplate.opsForValue().get(userInfoKey);

                if (StringUtils.hasText(userInfo)) {
                    log.debug("从缓存获取用户信息: {}", username);
                    return converter.fromTypedJson(userInfo);
                }
                return null;
            })
            .subscribeOn(Schedulers.boundedElastic())
            .onErrorResume(throwable -> {
                log.warn("从Redis缓存获取用户信息失败: {}", username, throwable);
                return Mono.empty();
            });
    }

    /**
     * 从认证服务获取用户信息。
     *
     * @param username 用户名
     * @return Mono<UserAuthDTO> 用户信息
     */
    private Mono<UserAuthDTO> getUserInfoFromService(String username) {
        log.debug("从认证服务获取用户信息: {}", username);

        return authServiceClient.refreshUserInfo(Long.valueOf(username))
            .retryWhen(Retry.backoff(3, Duration.ofMillis(100))
                .maxBackoff(Duration.ofSeconds(2))
                .filter(throwable -> !(throwable instanceof IllegalArgumentException))
            )
            .onErrorResume(throwable -> {
                log.error("从认证服务获取用户信息失败: {}", username, throwable);
                return Mono.empty();
            });
    }

    /**
     * 注入用户信息到请求头。
     *
     * @param mutate      请求构建器
     * @param userAuthDTO 用户信息
     */
    private void injectUserHeaders(ServerHttpRequest.Builder mutate, UserAuthDTO userAuthDTO) {
        try {
            String encodedUsername = URLEncoder.encode(userAuthDTO.getUsername(), StandardCharsets.UTF_8);
            mutate.header(HEADER_USER_NAME, encodedUsername);
            mutate.header(HEADER_USER_ID, userAuthDTO.getId().toString());
            mutate.header(HEADER_TENANT_ID, userAuthDTO.getTenantId().toString());

            log.debug("用户信息已注入请求头: userId={}, tenantId={}",
                userAuthDTO.getId(), userAuthDTO.getTenantId());
        } catch (Exception e) {
            log.error("注入用户信息到请求头失败", e);
        }
    }

    /**
     * 执行访问控制检查。
     *
     * @param exchange    服务器Web交换对象
     * @param chain       过滤器链
     * @param userAuthDTO 用户信息
     * @param mutate      请求构建器
     * @param sample      计时器样本
     * @param method      HTTP方法
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> performAccessControl(
        ServerWebExchange exchange,
        GatewayFilterChain chain,
        UserAuthDTO userAuthDTO,
        ServerHttpRequest.Builder mutate,
        Timer.Sample sample,
        HttpMethod method) {
        // 检查租户访问权限
        return checkTenantAccess(userAuthDTO.getTenantId())
            .flatMap(hasTenantAccess -> {
                if (!hasTenantAccess) {
                    sample.stop(Timer.builder("user_info_processing_duration")
                        .tag("result", "tenant_denied")
                        .register(meterRegistry));
                    log.warn("租户访问被拒绝: tenantId={}, userId={}",
                        userAuthDTO.getTenantId(), userAuthDTO.getId());
                    return handleTenantAccessDenied(exchange);
                }

                // 检查用户权限
                return checkUserPermission(userAuthDTO.getUsername(), method)
                    .flatMap(hasPermission -> {
                        if (!hasPermission) {
                            sample.stop(Timer.builder("user_info_processing_duration")
                                .tag("result", "permission_denied")
                                .register(meterRegistry));
                            log.warn("用户权限不足: username={}, path={}",
                                userAuthDTO.getUsername(), exchange.getRequest().getURI().getPath());
                            return handlePermissionDenied(exchange);
                        }

                        // 访问控制通过，继续处理请求
                        sample.stop(Timer.builder("user_info_processing_duration")
                            .tag("result", "success")
                            .register(meterRegistry));
                        log.debug("访问控制检查通过: username={}", userAuthDTO.getUsername());
                        return chain.filter(exchange.mutate().request(mutate.build()).build());
                    });
            });
    }

    /**
     * 检查租户访问权限。
     *
     * @param tenantId 租户ID
     * @return Mono<Boolean> 是否有访问权限
     */
    private Mono<Boolean> checkTenantAccess(Long tenantId) {
        return orgServiceClient.checkTenantAccess(tenantId)
            .retryWhen(Retry.backoff(2, Duration.ofMillis(100))
                .maxBackoff(Duration.ofSeconds(1))
            )
            .onErrorReturn(false) // 服务调用失败时默认不允许访问
            .doOnNext(hasAccess -> log.debug("租户访问权限检查: tenantId={}, hasAccess={}", tenantId, hasAccess));
    }

    /**
     * 检查用户权限。
     *
     * @param username 用户名
     * @param method   HTTP方法
     * @return Mono<Boolean> 是否有权限
     */
    private Mono<Boolean> checkUserPermission(String username, HttpMethod method) {
        return Mono.just(Boolean.TRUE);
//        return authServiceClient.verifyPermission(username, method.name())
//            .retryWhen(Retry.backoff(2, Duration.ofMillis(100))
//                .maxBackoff(Duration.ofSeconds(1))
//            )
//            .onErrorReturn(false) // 服务调用失败时默认不允许访问
//            .doOnNext(hasPermission -> log.debug("用户权限检查: username={}, hasPermission={}", username, hasPermission));
    }

    /**
     * 处理Token黑名单情况。
     *
     * @param exchange 服务器Web交换对象
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> handleTokenBlacklisted(org.springframework.web.server.ServerWebExchange exchange) {
        log.warn("Token已被列入黑名单，拒绝访问");
        return writeErrorResponse(exchange, HttpStatus.UNAUTHORIZED, SESSION_EXPIRED_MESSAGE);
    }

    /**
     * 处理用户未找到情况。
     *
     * @param exchange 服务器Web交换对象
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> handleUserNotFound(org.springframework.web.server.ServerWebExchange exchange) {
        log.warn("用户信息未找到，拒绝访问");
        return writeErrorResponse(exchange, HttpStatus.UNAUTHORIZED, "用户信息不存在");
    }

    /**
     * 处理租户访问被拒绝情况。
     *
     * @param exchange 服务器Web交换对象
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> handleTenantAccessDenied(org.springframework.web.server.ServerWebExchange exchange) {
        return writeErrorResponse(exchange, HttpStatus.FORBIDDEN, TENANT_ACCESS_DENIED_MESSAGE);
    }

    /**
     * 处理权限不足情况。
     *
     * @param exchange 服务器Web交换对象
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> handlePermissionDenied(org.springframework.web.server.ServerWebExchange exchange) {
        return writeErrorResponse(exchange, HttpStatus.FORBIDDEN, PERMISSION_DENIED_MESSAGE);
    }

    /**
     * 处理处理过程中的错误。
     *
     * @param exchange  服务器Web交换对象
     * @param throwable 异常
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> handleProcessingError(org.springframework.web.server.ServerWebExchange exchange, Throwable throwable) {
        log.error("用户信息处理过程中发生错误", throwable);
        return writeErrorResponse(exchange, HttpStatus.INTERNAL_SERVER_ERROR, "服务器内部错误");
    }

    /**
     * 写入错误响应。
     *
     * @param exchange 服务器Web交换对象
     * @param status   HTTP状态码
     * @param message  错误消息
     * @return Mono<Void> 处理结果
     */
    private Mono<Void> writeErrorResponse(org.springframework.web.server.ServerWebExchange exchange, HttpStatus status, String message) {
        ServerHttpResponse response = exchange.getResponse();
        response.setStatusCode(status);
        response.getHeaders().add("Content-Type", MediaType.TEXT_PLAIN_VALUE + ";charset=UTF-8");

        DataBuffer buffer = response.bufferFactory().wrap(message.getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 用户信息过滤器配置类。
     * 支持各种配置选项以控制过滤器行为。
     */
    public static class Config {

        // 是否启用用户信息注入
        private boolean enableUserInfoInjection = true;

        // 是否启用租户访问控制
        private boolean enableTenantAccessControl = true;

        // 是否启用权限检查
        private boolean enablePermissionCheck = true;

        // 是否启用Token黑名单检查
        private boolean enableTokenBlacklistCheck = true;

        // 用户信息缓存过期时间（分钟）
        private int userInfoCacheExpireMinutes = 30;

        // 服务调用重试次数
        private int serviceCallRetryAttempts = 3;

        // 服务调用超时时间（毫秒）
        private long serviceCallTimeoutMs = 5000;

        // 是否启用详细日志
        private boolean enableDetailedLogging = false;

        // 自定义错误消息
        private String customSessionExpiredMessage = SESSION_EXPIRED_MESSAGE;
        private String customPermissionDeniedMessage = PERMISSION_DENIED_MESSAGE;
        private String customTenantAccessDeniedMessage = TENANT_ACCESS_DENIED_MESSAGE;

        // Getters and Setters
        public boolean isEnableUserInfoInjection() {
            return enableUserInfoInjection;
        }

        public void setEnableUserInfoInjection(boolean enableUserInfoInjection) {
            this.enableUserInfoInjection = enableUserInfoInjection;
        }

        public boolean isEnableTenantAccessControl() {
            return enableTenantAccessControl;
        }

        public void setEnableTenantAccessControl(boolean enableTenantAccessControl) {
            this.enableTenantAccessControl = enableTenantAccessControl;
        }

        public boolean isEnablePermissionCheck() {
            return enablePermissionCheck;
        }

        public void setEnablePermissionCheck(boolean enablePermissionCheck) {
            this.enablePermissionCheck = enablePermissionCheck;
        }

        public boolean isEnableTokenBlacklistCheck() {
            return enableTokenBlacklistCheck;
        }

        public void setEnableTokenBlacklistCheck(boolean enableTokenBlacklistCheck) {
            this.enableTokenBlacklistCheck = enableTokenBlacklistCheck;
        }

        public int getUserInfoCacheExpireMinutes() {
            return userInfoCacheExpireMinutes;
        }

        public void setUserInfoCacheExpireMinutes(int userInfoCacheExpireMinutes) {
            this.userInfoCacheExpireMinutes = Math.max(1, userInfoCacheExpireMinutes);
        }

        public int getServiceCallRetryAttempts() {
            return serviceCallRetryAttempts;
        }

        public void setServiceCallRetryAttempts(int serviceCallRetryAttempts) {
            this.serviceCallRetryAttempts = Math.max(0, Math.min(10, serviceCallRetryAttempts));
        }

        public long getServiceCallTimeoutMs() {
            return serviceCallTimeoutMs;
        }

        public void setServiceCallTimeoutMs(long serviceCallTimeoutMs) {
            this.serviceCallTimeoutMs = Math.max(1000, serviceCallTimeoutMs);
        }

        public boolean isEnableDetailedLogging() {
            return enableDetailedLogging;
        }

        public void setEnableDetailedLogging(boolean enableDetailedLogging) {
            this.enableDetailedLogging = enableDetailedLogging;
        }

        public String getCustomSessionExpiredMessage() {
            return customSessionExpiredMessage;
        }

        public void setCustomSessionExpiredMessage(String customSessionExpiredMessage) {
            this.customSessionExpiredMessage = customSessionExpiredMessage != null ? customSessionExpiredMessage : SESSION_EXPIRED_MESSAGE;
        }

        public String getCustomPermissionDeniedMessage() {
            return customPermissionDeniedMessage;
        }

        public void setCustomPermissionDeniedMessage(String customPermissionDeniedMessage) {
            this.customPermissionDeniedMessage = customPermissionDeniedMessage != null ? customPermissionDeniedMessage : PERMISSION_DENIED_MESSAGE;
        }

        public String getCustomTenantAccessDeniedMessage() {
            return customTenantAccessDeniedMessage;
        }

        public void setCustomTenantAccessDeniedMessage(String customTenantAccessDeniedMessage) {
            this.customTenantAccessDeniedMessage = customTenantAccessDeniedMessage != null ? customTenantAccessDeniedMessage : TENANT_ACCESS_DENIED_MESSAGE;
        }

        @Override
        public String toString() {
            return "Config{" +
                "enableUserInfoInjection=" + enableUserInfoInjection +
                ", enableTenantAccessControl=" + enableTenantAccessControl +
                ", enablePermissionCheck=" + enablePermissionCheck +
                ", enableTokenBlacklistCheck=" + enableTokenBlacklistCheck +
                ", userInfoCacheExpireMinutes=" + userInfoCacheExpireMinutes +
                ", serviceCallRetryAttempts=" + serviceCallRetryAttempts +
                ", serviceCallTimeoutMs=" + serviceCallTimeoutMs +
                ", enableDetailedLogging=" + enableDetailedLogging +
                '}';
        }
    }
}
