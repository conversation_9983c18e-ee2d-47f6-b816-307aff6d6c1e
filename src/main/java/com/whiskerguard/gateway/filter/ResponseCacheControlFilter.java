package com.whiskerguard.gateway.filter;

import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

@Component
public class ResponseCacheControlFilter implements GatewayFilter, Ordered {

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        return chain
            .filter(exchange)
            .then(
                Mono.fromRunnable(() -> {
                    if (
                        "GET".equalsIgnoreCase(exchange.getRequest().getMethod() != null ? exchange.getRequest().getMethod().name() : null)
                    ) {
                        exchange.getResponse().getHeaders().add("Cache-Control", "max-age=60, public");
                    }
                })
            );
    }

    @Override
    public int getOrder() {
        return -1;
    }
}
