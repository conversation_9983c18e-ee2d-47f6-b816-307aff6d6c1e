package com.whiskerguard.gateway.filter;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.gateway.filter.GatewayFilter;
import org.springframework.cloud.gateway.filter.factory.AbstractGatewayFilterFactory;
import org.springframework.cloud.gateway.filter.ratelimit.KeyResolver;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;

/**
 * Filter for tenant-level rate limiting.
 * Uses tenant ID from X-Tenant-ID header for rate limiting.
 */
@Component
public class TenantRateLimiterGatewayFilterFactory extends AbstractGatewayFilterFactory<TenantRateLimiterGatewayFilterFactory.Config> {

    private final Logger log = LoggerFactory.getLogger(TenantRateLimiterGatewayFilterFactory.class);

    private final RedisRateLimiter rateLimiter;
    private final KeyResolver keyResolver;

    public TenantRateLimiterGatewayFilterFactory(
        @Qualifier("tenantRateLimiter") RedisRateLimiter rateLimiter,
        @Qualifier("tenantKeyResolver") KeyResolver keyResolver
    ) {
        super(Config.class);
        this.rateLimiter = rateLimiter;
        this.keyResolver = keyResolver;
    }

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            return keyResolver
                .resolve(exchange)
                .flatMap(key ->
                    rateLimiter
                        .isAllowed(key, String.valueOf(config.getReplenishRate()))
                        .flatMap(response -> {
                            ServerHttpResponse httpResponse = exchange.getResponse();
                            if (!response.isAllowed()) {
                                log.warn("Tenant rate limit exceeded for key: {}", key);
                                httpResponse.setStatusCode(HttpStatus.TOO_MANY_REQUESTS);
                                return httpResponse.setComplete();
                            }

                            // Add rate limit headers
                            httpResponse.getHeaders().add("X-RateLimit-Remaining", String.valueOf(config.getBurstCapacity()));
                            httpResponse.getHeaders().add("X-RateLimit-Limit", String.valueOf(config.getBurstCapacity()));
                            httpResponse.getHeaders().add("X-RateLimit-Reset", String.valueOf(config.getReplenishRate()));

                            return chain.filter(exchange);
                        })
                );
        };
    }

    public static class Config {

        private int replenishRate;
        private int burstCapacity;

        public int getReplenishRate() {
            return replenishRate;
        }

        public void setReplenishRate(int replenishRate) {
            this.replenishRate = replenishRate;
        }

        public int getBurstCapacity() {
            return burstCapacity;
        }

        public void setBurstCapacity(int burstCapacity) {
            this.burstCapacity = burstCapacity;
        }
    }
}
