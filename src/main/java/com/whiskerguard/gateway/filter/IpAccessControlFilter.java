package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.config.IpAccessControlProperties;
import jakarta.annotation.PostConstruct;
import org.apache.commons.net.util.SubnetUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.util.Objects;

@Component
public class IpAccessControlFilter implements GlobalFilter, Ordered {

    private static final Logger log = LoggerFactory.getLogger(IpAccessControlFilter.class);
    private final IpAccessControlProperties properties;

    public IpAccessControlFilter(IpAccessControlProperties properties) {
        this.properties = properties;
    }

    @PostConstruct
    public void init() {
        log.info("IP Access Control Properties loaded:");
        log.info("Whitelist mode: {}", properties.isWhitelistMode());
        log.info("Whitelist: {}", properties.getWhitelist());
        log.info("Blacklist: {}", properties.getBlacklist());
        log.info("Sensitive paths: {}", properties.getSensitivePaths());
    }

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        String clientIp = getClientIp(exchange);
        String path = exchange.getRequest().getURI().getPath();
        log.info("Access from IP: {}, path: {}", clientIp, path);

        // 首先检查黑名单
        if (isIpInBlacklist(clientIp)) {
            log.warn("Blocked access from blacklisted IP: {}", clientIp);
            return unauthorized(exchange);
        }

        // 然后检查敏感路径
        if (isSensitivePath(path)) {
            log.info("Checking sensitive path access for IP: {}", clientIp);
            if (!isIpInWhitelist(clientIp)) {
                log.warn("Blocked access to sensitive path from IP: {}", clientIp);
                return unauthorized(exchange);
            }
        }

        // 最后检查白名单模式
        if (properties.isWhitelistMode() && !isIpInWhitelist(clientIp)) {
            log.warn("Blocked access from non-whitelisted IP: {}", clientIp);
            return unauthorized(exchange);
        }

        log.info("Access allowed for IP: {}", clientIp);
        //构建新的exchange
        ServerHttpRequest newRequest = exchange.getRequest()
            .mutate().header("X-Forwarded-For", clientIp).build();
        ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();
        return chain.filter(newExchange);
    }

    @Override
    public int getOrder() {
        // 确保在认证过滤器之前执行
        return -200;
    }

    private String getClientIp(ServerWebExchange exchange) {
        String xForwardedFor = exchange.getRequest().getHeaders().getFirst("X-Forwarded-For");
        if (xForwardedFor != null && !xForwardedFor.isEmpty()) {
            String ip = xForwardedFor.split(",")[0].trim();
            log.info("Using X-Forwarded-For IP: {}", ip);
            return ip;
        }
        String ip = Objects.requireNonNull(exchange.getRequest().getRemoteAddress()).getAddress().getHostAddress();
        log.info("Using remote address IP: {}", ip);
        return ip;
    }

    private boolean isIpInBlacklist(String ip) {
        return properties.getBlacklist() != null && properties.getBlacklist().stream().anyMatch(cidr -> isIpInCidr(ip, cidr));
    }

    private boolean isIpInWhitelist(String ip) {
        return properties.getWhitelist() != null && properties.getWhitelist().stream().anyMatch(cidr -> isIpInCidr(ip, cidr));
    }

    private boolean isSensitivePath(String path) {
        return properties.getSensitivePaths() != null && properties.getSensitivePaths().stream().anyMatch(path::matches);
    }

    private boolean isIpInCidr(String ip, String cidr) {
        try {
            SubnetUtils utils = new SubnetUtils(cidr);
            return utils.getInfo().isInRange(ip);
        } catch (Exception e) {
            log.error("Error checking CIDR: {}", cidr, e);
            return false;
        }
    }

    private Mono<Void> unauthorized(ServerWebExchange exchange) {
        exchange.getResponse().setStatusCode(HttpStatus.FORBIDDEN);
        return exchange.getResponse().setComplete();
    }
}
