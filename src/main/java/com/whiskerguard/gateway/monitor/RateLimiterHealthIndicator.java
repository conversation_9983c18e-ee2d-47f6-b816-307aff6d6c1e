package com.whiskerguard.gateway.monitor;

import com.whiskerguard.gateway.config.RateLimiterProperties;
import io.micrometer.core.instrument.Counter;
import io.micrometer.core.instrument.MeterRegistry;
import java.time.Duration;
import java.time.Instant;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.cloud.gateway.filter.ratelimit.RedisRateLimiter;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

/**
 * 限流器组件健康监控器。
 * 监控Redis连接状态、本地降级使用情况和限流性能。
 *
 * 提供详细的健康信息包括：
 * - Redis连接状态
 * - 限流成功/失败率
 * - 本地降级激活频率
 * - 性能指标和趋势
 *
 * <AUTHOR> Team
 * @version 1.0
 */
@Component
public class RateLimiterHealthIndicator {

    private static final Logger log = LoggerFactory.getLogger(RateLimiterHealthIndicator.class);

    private final RedisTemplate<String, String> redisTemplate;
    private final MeterRegistry meterRegistry;
    private final RateLimiterProperties rateLimiterProperties;
    private final ScheduledExecutorService scheduler;

    // 健康状态跟踪
    private final Map<String, HealthMetrics> healthMetrics = new ConcurrentHashMap<>();
    private volatile boolean redisHealthy = true;
    private volatile Instant lastRedisCheck = Instant.now();
    private volatile Instant lastHealthUpdate = Instant.now();

    // 性能跟踪
    private final Map<String, PerformanceMetrics> performanceMetrics = new ConcurrentHashMap<>();

    public RateLimiterHealthIndicator(
        @Qualifier("rateLimiterRedisTemplate") RedisTemplate<String, String> redisTemplate,
        MeterRegistry meterRegistry,
        RateLimiterProperties rateLimiterProperties
    ) {
        this.redisTemplate = redisTemplate;
        this.meterRegistry = meterRegistry;
        this.rateLimiterProperties = rateLimiterProperties;
        this.scheduler = Executors.newScheduledThreadPool(2);

        // 启动定期健康检查
        startPeriodicHealthChecks();

        log.info("限流器健康指示器已初始化，启用定期健康检查");
    }

    /**
     * 获取限流器当前的健康状态。
     *
     * @return 包含健康状态和详细信息的Map
     */
    public Map<String, Object> getHealthStatus() {
        Map<String, Object> healthStatus = new HashMap<>();

        try {
            // 检查Redis连接状态
            boolean redisUp = checkRedisHealth();

            // 收集指标
            Map<String, Object> details = new HashMap<>();
            details.put("redis", createRedisHealthDetails(redisUp));
            details.put("rateLimiting", createRateLimitingHealthDetails());
            details.put("performance", createPerformanceDetails());
            details.put("configuration", createConfigurationDetails());
            details.put("lastUpdated", lastHealthUpdate.toString());

            // 确定整体健康状态
            String status;
            if (redisUp && isRateLimitingHealthy()) {
                status = "UP";
            } else if (isLocalFallbackWorking()) {
                status = "DEGRADED";
            } else {
                status = "DOWN";
            }

            healthStatus.put("status", status);
            healthStatus.put("details", details);

        } catch (Exception e) {
            log.error("健康检查过程中发生错误", e);
            healthStatus.put("status", "DOWN");
            healthStatus.put("error", e.getMessage());
        }

        return healthStatus;
    }

    /**
     * 检查Redis连接状态和性能。
     */
    private boolean checkRedisHealth() {
        try {
            long startTime = System.currentTimeMillis();
            redisTemplate.opsForValue().set("health:rate-limiter:ping", "pong", Duration.ofSeconds(10));
            String result = redisTemplate.opsForValue().get("health:rate-limiter:ping");
            long responseTime = System.currentTimeMillis() - startTime;

            boolean healthy = "pong".equals(result) && responseTime < 1000; // 1秒阈值

            redisHealthy = healthy;
            lastRedisCheck = Instant.now();

            // 记录指标
            Counter redisHealthCounter = meterRegistry.counter("rate_limiter_redis_health_checks_total",
                "status", healthy ? "success" : "failure");
            redisHealthCounter.increment();

            meterRegistry.timer("rate_limiter_redis_response_time").record(responseTime, TimeUnit.MILLISECONDS);

            log.debug("Redis健康检查: 健康={}, 响应时间={}ms", healthy, responseTime);
            return healthy;

        } catch (Exception e) {
            log.warn("Redis健康检查失败", e);
            redisHealthy = false;
            lastRedisCheck = Instant.now();

            meterRegistry.counter("rate_limiter_redis_health_checks_total", "status", "failure").increment();
            return false;
        }
    }

    /**
     * 创建Redis健康详情。
     */
    private Map<String, Object> createRedisHealthDetails(boolean redisUp) {
        Map<String, Object> redisDetails = new HashMap<>();
        redisDetails.put("status", redisUp ? "UP" : "DOWN");
        redisDetails.put("lastCheck", lastRedisCheck.toString());

        if (redisUp) {
            redisDetails.put("responseTime", getRedisResponseTime());
        }

        return redisDetails;
    }

    /**
     * 创建限流健康详情。
     */
    private Map<String, Object> createRateLimitingHealthDetails() {
        Map<String, Object> rateLimitDetails = new HashMap<>();

        // Get counters from meter registry
        Counter allowedCounter = meterRegistry.find("rate_limiter_allowed_total").counter();
        Counter limitedCounter = meterRegistry.find("rate_limiter_limited_total").counter();
        Counter fallbackCounter = meterRegistry.find("rate_limiter_local_fallback_total").counter();
        Counter redisFailureCounter = meterRegistry.find("rate_limiter_redis_failures_total").counter();

        double allowedCount = allowedCounter != null ? allowedCounter.count() : 0;
        double limitedCount = limitedCounter != null ? limitedCounter.count() : 0;
        double fallbackCount = fallbackCounter != null ? fallbackCounter.count() : 0;
        double redisFailureCount = redisFailureCounter != null ? redisFailureCounter.count() : 0;

        double totalRequests = allowedCount + limitedCount;
        double successRate = totalRequests > 0 ? (allowedCount / totalRequests) * 100 : 100;
        double fallbackRate = totalRequests > 0 ? (fallbackCount / totalRequests) * 100 : 0;

        rateLimitDetails.put("totalRequests", (long) totalRequests);
        rateLimitDetails.put("allowedRequests", (long) allowedCount);
        rateLimitDetails.put("limitedRequests", (long) limitedCount);
        rateLimitDetails.put("fallbackActivations", (long) fallbackCount);
        rateLimitDetails.put("redisFailures", (long) redisFailureCount);
        rateLimitDetails.put("successRate", String.format("%.2f%%", successRate));
        rateLimitDetails.put("fallbackRate", String.format("%.2f%%", fallbackRate));

        return rateLimitDetails;
    }

    /**
     * 创建性能详情。
     */
    private Map<String, Object> createPerformanceDetails() {
        Map<String, Object> performanceDetails = new HashMap<>();

        // 获取计时指标
        var allowedTimer = meterRegistry.find("rate_limiter_duration").tag("status", "allowed").timer();
        var limitedTimer = meterRegistry.find("rate_limiter_duration").tag("status", "limited").timer();

        if (allowedTimer != null) {
            performanceDetails.put("averageAllowedResponseTime",
                String.format("%.2fms", allowedTimer.mean(TimeUnit.MILLISECONDS)));
        }

        if (limitedTimer != null) {
            performanceDetails.put("averageLimitedResponseTime",
                String.format("%.2fms", limitedTimer.mean(TimeUnit.MILLISECONDS)));
        }

        return performanceDetails;
    }

    /**
     * 创建配置详情。
     */
    private Map<String, Object> createConfigurationDetails() {
        Map<String, Object> configDetails = new HashMap<>();

        Map<String, RateLimiterProperties.Rule> rules = rateLimiterProperties.getRules();
        Map<String, Object> rulesDetails = new HashMap<>();

        rules.forEach((name, rule) -> {
            Map<String, Object> ruleDetails = new HashMap<>();
            ruleDetails.put("replenishRate", rule.getReplenishRate());
            ruleDetails.put("burstCapacity", rule.getBurstCapacity());
            ruleDetails.put("algorithm", rule.getAlgorithm());
            ruleDetails.put("enableLocalFallback", rule.isEnableLocalFallback());
            ruleDetails.put("enableRetry", rule.isEnableRetry());
            rulesDetails.put(name, ruleDetails);
        });

        configDetails.put("rules", rulesDetails);
        configDetails.put("totalRules", rules.size());

        return configDetails;
    }

    /**
     * 检查限流是否总体健康。
     */
    private boolean isRateLimitingHealthy() {
        Counter redisFailureCounter = meterRegistry.find("rate_limiter_redis_failures_total").counter();
        Counter totalCounter = meterRegistry.find("rate_limiter_allowed_total").counter();

        if (redisFailureCounter == null || totalCounter == null) {
            return true; // 暂无数据，假设健康
        }

        double failureCount = redisFailureCounter.count();
        double totalCount = totalCounter.count();

        if (totalCount == 0) {
            return true; // 暂无请求
        }

        double failureRate = failureCount / totalCount;
        return failureRate < 0.1; // 失败率小于10%
    }

    /**
     * 检查本地降级是否正常工作。
     */
    private boolean isLocalFallbackWorking() {
        Counter fallbackCounter = meterRegistry.find("rate_limiter_local_fallback_total").counter();
        return fallbackCounter != null && fallbackCounter.count() > 0;
    }

    /**
     * 从指标中获取Redis响应时间。
     */
    private String getRedisResponseTime() {
        var timer = meterRegistry.find("rate_limiter_redis_response_time").timer();
        if (timer != null) {
            return String.format("%.2fms", timer.mean(TimeUnit.MILLISECONDS));
        }
        return "N/A";
    }

    /**
     * 启动定期健康检查。
     */
    private void startPeriodicHealthChecks() {
        // 每30秒进行Redis健康检查
        scheduler.scheduleAtFixedRate(() -> {
            try {
                checkRedisHealth();
                lastHealthUpdate = Instant.now();
            } catch (Exception e) {
                log.error("定期Redis健康检查过程中发生错误", e);
            }
        }, 30, 30, TimeUnit.SECONDS);

        // 每5分钟清理旧指标
        scheduler.scheduleAtFixedRate(() -> {
            try {
                cleanupOldMetrics();
            } catch (Exception e) {
                log.error("指标清理过程中发生错误", e);
            }
        }, 300, 300, TimeUnit.SECONDS);
    }

    /**
     * 清理旧指标以防止内存泄漏。
     */
    private void cleanupOldMetrics() {
        Instant cutoff = Instant.now().minus(Duration.ofHours(1));

        healthMetrics.entrySet().removeIf(entry ->
            entry.getValue().getLastUpdated().isBefore(cutoff));

        performanceMetrics.entrySet().removeIf(entry ->
            entry.getValue().getLastUpdated().isBefore(cutoff));

        log.debug("已清理旧的健康和性能指标");
    }

    /**
     * 健康指标持有者。
     */
    private static class HealthMetrics {
        private final Instant lastUpdated;
        private final boolean healthy;

        public HealthMetrics(boolean healthy) {
            this.lastUpdated = Instant.now();
            this.healthy = healthy;
        }

        public Instant getLastUpdated() { return lastUpdated; }
        public boolean isHealthy() { return healthy; }
    }

    /**
     * 性能指标持有者。
     */
    private static class PerformanceMetrics {
        private final Instant lastUpdated;
        private final long responseTime;

        public PerformanceMetrics(long responseTime) {
            this.lastUpdated = Instant.now();
            this.responseTime = responseTime;
        }

        public Instant getLastUpdated() { return lastUpdated; }
        public long getResponseTime() { return responseTime; }
    }
}
