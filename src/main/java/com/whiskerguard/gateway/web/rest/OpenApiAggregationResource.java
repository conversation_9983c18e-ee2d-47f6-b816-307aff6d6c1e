package com.whiskerguard.gateway.web.rest;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.reactive.function.client.WebClient;
import reactor.core.publisher.Mono;

@RestController
public class OpenApiAggregationResource {

    private final WebClient webClient;
    private final List<String> downstreamServices;

    public OpenApiAggregationResource(
        WebClient.Builder webClientBuilder,
        @Value("${gateway.openapi.services:whiskerguard-auth-service,user-service,order-service}") List<String> downstreamServices
    ) {
        this.webClient = webClientBuilder.build();
        this.downstreamServices = downstreamServices;
    }

    @GetMapping("/v3/api-docs-aggregate")
    public Mono<Map<String, Object>> aggregateOpenApiDocs() {
        return Mono.zip(
            downstreamServices
                .stream()
                .map(service ->
                    webClient
                        .get()
                        .uri("http://" + service + "/v3/api-docs")
                        .retrieve()
                        .bodyToMono(Map.class)
                        .onErrorReturn(Map.of("error", "unavailable"))
                )
                .collect(Collectors.toList()),
            results -> {
                Map<String, Object> aggregated = new java.util.HashMap<>();
                for (int i = 0; i < downstreamServices.size(); i++) {
                    aggregated.put(downstreamServices.get(i), results[i]);
                }
                return aggregated;
            }
        );
    }
}
