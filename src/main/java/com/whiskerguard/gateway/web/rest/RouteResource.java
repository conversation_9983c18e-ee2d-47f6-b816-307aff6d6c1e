package com.whiskerguard.gateway.web.rest;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.whiskerguard.gateway.config.ConsulRouteProperties;
import com.whiskerguard.gateway.config.DynamicRouteConfiguration;
import com.whiskerguard.gateway.config.RouteDefinition;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cloud.gateway.event.RefreshRoutesEvent;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.data.redis.core.ReactiveRedisTemplate;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api/gateway/route-definitions")
public class RouteResource {

    private static final Logger log = LoggerFactory.getLogger(RouteResource.class);
    private static final String REDIS_ROUTE_KEY = "gateway:routes";

    private final DynamicRouteConfiguration routeConfiguration;
    private final ReactiveRedisTemplate<String, String> redisTemplate;
    private final ObjectMapper objectMapper;
    private final RouteLocator routeLocator;
    private final ApplicationEventPublisher publisher;
    private final ConsulRouteProperties consulRouteProperties;

    public RouteResource(
        DynamicRouteConfiguration routeConfiguration,
        ReactiveRedisTemplate<String, String> redisTemplate,
        ObjectMapper objectMapper,
        RouteLocator routeLocator,
        ApplicationEventPublisher publisher,
        ConsulRouteProperties consulRouteProperties
    ) {
        this.routeConfiguration = routeConfiguration;
        this.redisTemplate = redisTemplate;
        this.objectMapper = objectMapper;
        this.routeLocator = routeLocator;
        this.publisher = publisher;
        this.consulRouteProperties = consulRouteProperties;
    }

    @GetMapping
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<List<RouteDefinition>>> getRoutes() {
        return redisTemplate
            .opsForValue()
            .get(REDIS_ROUTE_KEY)
            .flatMap(routesJson -> {
                try {
                    List<RouteDefinition> routes = objectMapper.readValue(routesJson, new TypeReference<List<RouteDefinition>>() {});
                    return Mono.<ResponseEntity<List<RouteDefinition>>>just(ResponseEntity.ok(routes));
                } catch (Exception e) {
                    log.error("Error parsing routes from Redis", e);
                    return Mono.<ResponseEntity<List<RouteDefinition>>>just(ResponseEntity.internalServerError().build());
                }
            })
            .switchIfEmpty(Mono.<ResponseEntity<List<RouteDefinition>>>just(ResponseEntity.ok(List.of())));
    }

    @PostMapping
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<Void>> updateRoutes(@RequestBody List<RouteDefinition> routes) {
        try {
            routeConfiguration.saveRoutes(routes);
            return Mono.just(ResponseEntity.<Void>ok().build());
        } catch (Exception e) {
            log.error("Error saving routes", e);
            return Mono.just(ResponseEntity.<Void>internalServerError().build());
        }
    }

    @DeleteMapping("/{id}")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<Void>> deleteRoute(@PathVariable String id) {
        return redisTemplate
            .opsForValue()
            .get(REDIS_ROUTE_KEY)
            .flatMap(routesJson -> {
                try {
                    List<RouteDefinition> routes = objectMapper.readValue(routesJson, new TypeReference<List<RouteDefinition>>() {});
                    routes.removeIf(route -> route.getId().equals(id));
                    routeConfiguration.saveRoutes(routes);
                    return Mono.<ResponseEntity<Void>>just(ResponseEntity.ok().build());
                } catch (Exception e) {
                    log.error("Error updating routes", e);
                    return Mono.<ResponseEntity<Void>>just(ResponseEntity.internalServerError().build());
                }
            })
            .switchIfEmpty(Mono.<ResponseEntity<Void>>just(ResponseEntity.notFound().build()));
    }

    @GetMapping("/status")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<Map<String, Object>>> getRouteStatus() {
        return routeLocator
            .getRoutes()
            .collectList()
            .map(routes -> {
                Map<String, Object> status = new HashMap<>();
                status.put("totalRoutes", routes.size());
                status.put("activeRoutes", routes.size());
                status.put(
                    "routes",
                    routes
                        .stream()
                        .map(route -> {
                            Map<String, Object> routeInfo = new HashMap<>();
                            routeInfo.put("id", route.getId());
                            routeInfo.put("uri", route.getUri().toString());
                            routeInfo.put("order", route.getOrder());
                            routeInfo.put("predicates", route.getPredicate().toString());
                            routeInfo.put("filters", route.getFilters().toString());
                            return routeInfo;
                        })
                        .toList()
                );
                return ResponseEntity.ok(status);
            });
    }

    @PostMapping("/refresh")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<Void>> refreshRoutes() {
        return Mono.fromRunnable(() -> {
            publisher.publishEvent(new RefreshRoutesEvent(this));
            log.info("Routes refresh event published");
        }).then(Mono.just(ResponseEntity.<Void>ok().build()));
    }

    @GetMapping("/source")
    @PreAuthorize("hasAuthority('ROLE_ADMIN')")
    public Mono<ResponseEntity<Map<String, Object>>> getRouteSource() {
        Map<String, Object> source = new HashMap<>();
        source.put("redis", true);
        source.put("consul", consulRouteProperties.isEnabled());
        source.put("consulKey", consulRouteProperties.getRouteKey());
        source.put("watchEnabled", consulRouteProperties.isWatchEnabled());
        source.put("watchDelay", consulRouteProperties.getWatchDelay());
        return Mono.just(ResponseEntity.ok(source));
    }
}
