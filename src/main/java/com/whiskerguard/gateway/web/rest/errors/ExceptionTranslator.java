package com.whiskerguard.gateway.web.rest.errors;

import static org.springframework.core.annotation.AnnotatedElementUtils.findMergedAnnotation;

import com.fasterxml.jackson.databind.ObjectMapper;
import feign.FeignException;
import jakarta.validation.ConstraintViolation;
import jakarta.validation.ConstraintViolationException;
import java.net.URI;
import java.util.*;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.web.error.ErrorAttributeOptions;
import org.springframework.boot.web.reactive.error.DefaultErrorAttributes;
import org.springframework.core.annotation.Order;
import org.springframework.dao.ConcurrencyFailureException;
import org.springframework.dao.DataAccessException;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.stereotype.Component;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.support.WebExchangeBindException;
import org.springframework.web.reactive.function.server.HandlerStrategies;
import org.springframework.web.reactive.function.server.ServerRequest;
import org.springframework.web.server.ResponseStatusException;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import tech.jhipster.web.rest.errors.ExceptionTranslation;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause;
import tech.jhipster.web.rest.errors.ProblemDetailWithCause.ProblemDetailWithCauseBuilder;
import tech.jhipster.web.util.HeaderUtil;

/**
 * WebFlux 异常处理器，用于将服务器端异常转换为客户端友好的JSON结构
 * 错误响应遵循RFC7807 - HTTP API的问题详情规范
 */
@Component
@Order(-2)
public class ExceptionTranslator extends DefaultErrorAttributes implements ExceptionTranslation {

    private static final String FIELD_ERRORS_KEY = "fieldErrors";
    private static final String MESSAGE_KEY = "message";
    private static final String PATH_KEY = "path";
    private static final boolean CASUAL_CHAIN_ENABLED = false;

    private static final Logger LOG = LoggerFactory.getLogger(ExceptionTranslator.class);

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    @Override
    public Map<String, Object> getErrorAttributes(ServerRequest request, ErrorAttributeOptions options) {
        Map<String, Object> errorAttributes = super.getErrorAttributes(request, options);
        Throwable error = getError(request);

        if (error != null) {
            LOG.debug("Converting Exception to Problem Details:", error);
            ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(error, request);

            // 将 ProblemDetailWithCause 转换为 Map
            errorAttributes.clear();
            errorAttributes.put("type", pdCause.getType().toString());
            errorAttributes.put("title", pdCause.getTitle());
            errorAttributes.put("status", pdCause.getStatus());
            errorAttributes.put("detail", pdCause.getDetail());
            errorAttributes.put("instance", pdCause.getInstance() != null ? pdCause.getInstance().toString() : null);

            if (pdCause.getProperties() != null) {
                errorAttributes.putAll(pdCause.getProperties());
            }
        }

        return errorAttributes;
    }

    @Override
    public Mono<ResponseEntity<Object>> handleAnyException(Throwable ex, ServerWebExchange exchange) {
        LOG.debug("Converting Exception to Problem Details:", ex);
        // 从 ServerWebExchange 创建 ServerRequest
        ServerRequest request = ServerRequest.create(exchange, HandlerStrategies.withDefaults().messageReaders());
        ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(ex, request);

        HttpStatus status = HttpStatus.valueOf(pdCause.getStatus());
        return Mono.just(new ResponseEntity<>(pdCause, status));
    }

    /**
     * 处理FeignException异常的特殊逻辑
     */
    private ProblemDetailWithCause handleFeignException(FeignException ex, ServerRequest request) {
        LOG.debug("Handling FeignException:", ex);

        try {
            // 尝试解析远程服务返回的错误响应
            String responseBody = ex.contentUTF8();
            if (StringUtils.isNotBlank(responseBody)) {
                ObjectMapper objectMapper = new ObjectMapper();
                // 尝试将响应体解析为Map，然后手动构建ProblemDetailWithCause
                @SuppressWarnings("unchecked")
                Map<String, Object> responseMap = objectMapper.readValue(responseBody, Map.class);

                // 手动构建ProblemDetailWithCause对象
                ProblemDetailWithCause remoteProblem = ProblemDetailWithCauseBuilder.instance().withStatus(ex.status()).build();

                // 设置基本属性
                if (responseMap.containsKey("type")) {
                    String typeStr = responseMap.get("type").toString();
                    try {
                        remoteProblem.setType(URI.create(typeStr));
                    } catch (Exception e) {
                        LOG.warn("Invalid URI in remote response type: {}", typeStr);
                    }
                }

                if (responseMap.containsKey("title")) {
                    remoteProblem.setTitle(responseMap.get("title").toString());
                }

                if (responseMap.containsKey("detail")) {
                    remoteProblem.setDetail(responseMap.get("detail").toString());
                }

                if (responseMap.containsKey("instance")) {
                    String instanceStr = responseMap.get("instance").toString();
                    try {
                        remoteProblem.setInstance(URI.create(instanceStr));
                    } catch (Exception e) {
                        LOG.warn("Invalid URI in remote response instance: {}", instanceStr);
                    }
                }

                if (responseMap.containsKey("status")) {
                    Object statusObj = responseMap.get("status");
                    if (statusObj instanceof Number) {
                        remoteProblem.setStatus(((Number) statusObj).intValue());
                    }
                }

                // 设置properties
                Map<String, Object> properties = new HashMap<>();
                for (Map.Entry<String, Object> entry : responseMap.entrySet()) {
                    String key = entry.getKey();
                    // 跳过标准的Problem Details字段
                    if (
                        !"type".equals(key) &&
                        !"title".equals(key) &&
                        !"detail".equals(key) &&
                        !"instance".equals(key) &&
                        !"status".equals(key)
                    ) {
                        properties.put(key, entry.getValue());
                    }
                }
                if (!properties.isEmpty()) {
                    remoteProblem.setProperties(properties);
                }

                return remoteProblem;
            }
        } catch (Exception parseException) {
            LOG.warn("Failed to parse FeignException response body, falling back to default handling", parseException);
        }

        // 如果解析失败，检查是否为502 Bad Gateway，提供特殊处理
        if (ex.status() == 502) {
            return ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.BAD_GATEWAY.value())
                .withTitle("服务繁忙，请稍后再试")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.502")
                .withProperty("path", getPathValue(request))
                .build();
        } else if (ex.status() == 404) {
            return ProblemDetailWithCauseBuilder.instance()
                .withStatus(HttpStatus.NOT_FOUND.value())
                .withTitle("服务初始化中，请稍后再试")
                .withDetail(getOriginalExceptionDetail(ex))
                .withProperty("message", "error.http.404")
                .withProperty("path", getPathValue(request))
                .build();
        }

        // 其他情况使用默认的异常处理逻辑
        return wrapAndCustomizeProblem(ex, request);
    }

    /**
     * 专门处理Bean Validation约束违反异常
     */
    private ProblemDetailWithCause handleConstraintViolationException(ConstraintViolationException ex, ServerRequest request) {
        LOG.debug("Converting ConstraintViolationException to Problem Details:", ex);
        ProblemDetailWithCause pdCause = wrapAndCustomizeProblem(ex, request);

        // 添加约束违反的详细信息
        Map<String, Object> properties = pdCause.getProperties();
        if (properties == null) {
            properties = new HashMap<>();
        }

        // 收集所有约束违反信息
        List<ConstraintViolationVM> constraintViolations = ex
            .getConstraintViolations()
            .stream()
            .map(violation ->
                new ConstraintViolationVM(
                    violation.getPropertyPath().toString(),
                    violation.getMessage(),
                    violation.getInvalidValue() != null ? violation.getInvalidValue().toString() : null
                )
            )
            .collect(Collectors.toList());

        properties.put("constraintViolations", constraintViolations);
        pdCause.setProperties(properties);

        return pdCause;
    }

    /**
     * 处理WebExchangeBindException异常（WebFlux中的参数绑定异常）
     */
    private ProblemDetailWithCause handleWebExchangeBindException(WebExchangeBindException ex, ServerRequest request) {
        LOG.debug("Converting WebExchangeBindException to Problem Details:", ex);

        ProblemDetailWithCause pdCause = ProblemDetailWithCauseBuilder.instance()
            .withStatus(HttpStatus.BAD_REQUEST.value())
            .withTitle("请求参数验证失败")
            .withDetail("请检查请求参数是否正确")
            .withProperty("message", "error.validation")
            .withProperty("path", getPathValue(request))
            .build();

        // 添加字段错误信息
        Map<String, Object> properties = pdCause.getProperties();
        if (properties == null) {
            properties = new HashMap<>();
        }

        List<FieldErrorVM> fieldErrors = ex
            .getBindingResult()
            .getFieldErrors()
            .stream()
            .map(f ->
                new FieldErrorVM(
                    f.getObjectName().replaceFirst("DTO$", ""),
                    f.getField(),
                    StringUtils.isNotBlank(f.getDefaultMessage()) ? f.getDefaultMessage() : f.getCode()
                )
            )
            .collect(Collectors.toList());

        properties.put(FIELD_ERRORS_KEY, fieldErrors);
        pdCause.setProperties(properties);

        return pdCause;
    }

    protected ProblemDetailWithCause wrapAndCustomizeProblem(Throwable ex, ServerRequest request) {
        // 特殊异常类型的处理
        if (ex instanceof FeignException feignException) {
            return handleFeignException(feignException, request);
        }
        if (ex instanceof ConstraintViolationException constraintException) {
            return handleConstraintViolationException(constraintException, request);
        }
        if (ex instanceof WebExchangeBindException bindException) {
            return handleWebExchangeBindException(bindException, request);
        }

        return customizeProblem(getProblemDetailWithCause(ex), ex, request);
    }

    private ProblemDetailWithCause getProblemDetailWithCause(Throwable ex) {
        if (ex instanceof ResponseStatusException responseStatusException) {
            return ProblemDetailWithCauseBuilder.instance()
                .withStatus(responseStatusException.getStatusCode().value())
                .withTitle(responseStatusException.getReason())
                .build();
        }
        return ProblemDetailWithCauseBuilder.instance().withStatus(toStatus(ex).value()).build();
    }

    protected ProblemDetailWithCause customizeProblem(ProblemDetailWithCause problem, Throwable err, ServerRequest request) {
        if (problem.getStatus() <= 0) problem.setStatus(toStatus(err));

        if (problem.getType().equals(URI.create("about:blank"))) problem.setType(getMappedType(err));

        // 首先确定message字段的值
        String messageKey = getMappedMessageKey(err) != null ? getMappedMessageKey(err) : "error.http." + problem.getStatus();

        // 统一处理所有异常的title，优先使用异常消息，然后考虑message字段的友好描述
        String customTitle = getExceptionMessageAsTitle(err);
        if (customTitle != null) {
            problem.setTitle(customTitle);
        } else {
            // 如果没有合适的异常消息，尝试从message字段生成友好的title
            String messageBasedTitle = getMessageBasedTitle(messageKey, err);
            if (messageBasedTitle != null) {
                problem.setTitle(messageBasedTitle);
            } else {
                // 最后使用默认的title提取逻辑
                String title = extractTitle(err, problem.getStatus());
                String problemTitle = problem.getTitle();
                if (problemTitle == null || !problemTitle.equals(title)) {
                    problem.setTitle(title);
                }
            }
        }

        if (problem.getDetail() == null) {
            // 为detail字段提供详细信息，区别于title的简洁描述
            String detailedInfo = getDetailedErrorInfo(err, problem.getStatus());
            problem.setDetail(detailedInfo);
        }

        Map<String, Object> problemProperties = problem.getProperties();
        if (problemProperties == null || !problemProperties.containsKey(MESSAGE_KEY)) {
            problem.setProperty(MESSAGE_KEY, messageKey);
        }

        if (problemProperties == null || !problemProperties.containsKey(PATH_KEY)) problem.setProperty(PATH_KEY, getPathValue(request));

        if (
            (err instanceof WebExchangeBindException bindException) &&
            (problemProperties == null || !problemProperties.containsKey(FIELD_ERRORS_KEY))
        ) problem.setProperty(FIELD_ERRORS_KEY, getFieldErrors(bindException));

        problem.setCause(buildCause(err.getCause(), request).orElse(null));

        return problem;
    }

    private String extractTitle(Throwable err, int statusCode) {
        String customizedTitle = getCustomizedTitle(err);
        if (customizedTitle != null) {
            return customizedTitle;
        }

        // 如果没有自定义title，则使用ResponseStatus或HTTP状态码的默认描述
        return extractTitleForResponseStatus(err, statusCode);
    }

    private List<FieldErrorVM> getFieldErrors(WebExchangeBindException ex) {
        return ex
            .getBindingResult()
            .getFieldErrors()
            .stream()
            .map(f ->
                new FieldErrorVM(
                    f.getObjectName().replaceFirst("DTO$", ""),
                    f.getField(),
                    StringUtils.isNotBlank(f.getDefaultMessage()) ? f.getDefaultMessage() : f.getCode()
                )
            )
            .collect(Collectors.toList());
    }

    private String extractTitleForResponseStatus(Throwable err, int statusCode) {
        ResponseStatus specialStatus = extractResponseStatus(err);
        return specialStatus == null ? HttpStatus.valueOf(statusCode).getReasonPhrase() : specialStatus.reason();
    }

    private String extractURI(ServerRequest request) {
        return request != null ? request.path() : StringUtils.EMPTY;
    }

    private HttpStatus toStatus(final Throwable throwable) {
        // 处理 ResponseStatusException
        if (throwable instanceof ResponseStatusException responseStatusException) {
            return HttpStatus.valueOf(responseStatusException.getStatusCode().value());
        }

        return Optional.ofNullable(getMappedStatus(throwable)).orElse(
            Optional.ofNullable(resolveResponseStatus(throwable))
                .map(status -> HttpStatus.valueOf(status.value().value()))
                .orElse(HttpStatus.INTERNAL_SERVER_ERROR)
        );
    }

    private ResponseStatus extractResponseStatus(final Throwable throwable) {
        return resolveResponseStatus(throwable);
    }

    private ResponseStatus resolveResponseStatus(final Throwable type) {
        final ResponseStatus candidate = findMergedAnnotation(type.getClass(), ResponseStatus.class);
        return candidate == null && type.getCause() != null ? resolveResponseStatus(type.getCause()) : candidate;
    }

    private URI getMappedType(Throwable err) {
        if (err instanceof WebExchangeBindException) return ErrorConstants.CONSTRAINT_VIOLATION_TYPE;
        if (err instanceof ConstraintViolationException) return ErrorConstants.CONSTRAINT_VIOLATION_TYPE;
        return ErrorConstants.DEFAULT_TYPE;
    }

    private String getMappedMessageKey(Throwable err) {
        if (err instanceof WebExchangeBindException) {
            return ErrorConstants.ERR_VALIDATION;
        } else if (err instanceof ConstraintViolationException) {
            return ErrorConstants.ERR_VALIDATION;
        } else if (err instanceof ConcurrencyFailureException || err.getCause() instanceof ConcurrencyFailureException) {
            return ErrorConstants.ERR_CONCURRENCY_FAILURE;
        }
        return null;
    }

    /**
     * 根据message字段的错误码生成用户友好的title
     * 将程序错误码转换为中文描述
     */
    private String getMessageBasedTitle(String messageKey, Throwable err) {
        if (messageKey == null) {
            return null;
        }

        // 处理特定的错误码
        switch (messageKey) {
            case ErrorConstants.ERR_VALIDATION:
                return "请求参数验证失败";
            case ErrorConstants.ERR_CONCURRENCY_FAILURE:
                return "操作冲突，请稍后重试";
            case "error.http.400":
                return "请求参数错误";
            case "error.http.401":
                return "身份验证失败";
            case "error.http.403":
                return "权限不足";
            case "error.http.404":
                return "请求的资源不存在";
            case "error.http.405":
                return "请求方法不支持";
            case "error.http.409":
                return "请求冲突";
            case "error.http.500":
                return "系统繁忙，请稍后再试";
            case "error.http.502":
                return "服务繁忙，请稍后再试";
            case "error.http.503":
                return "服务暂时不可用";
            case "error.http.504":
                return "请求超时，请稍后再试";
            default:
                // 对于其他错误码，尝试从异常类型推断
                return getCustomizedTitle(err);
        }
    }

    private String getCustomizedTitle(Throwable err) {
        // 对于特定异常类型，如果没有消息，则返回默认title
        if (err instanceof WebExchangeBindException) {
            return "请求参数无效";
        }
        if (err instanceof ConstraintViolationException) {
            return "参数验证失败";
        }
        if (err instanceof BadCredentialsException) {
            return "身份验证失败";
        }
        if (err instanceof AccessDeniedException) {
            return "权限不足";
        }
        if (err instanceof ConcurrencyFailureException) {
            return "操作冲突，请稍后重试";
        }
        if (err instanceof DataAccessException) {
            return "数据访问错误";
        }

        return null;
    }

    /**
     * 获取异常消息作为title
     * 统一处理所有异常，优先使用异常的message，如果为空则使用cause的message
     * 特别处理Jakarta Bean Validation异常，提取验证注解中的message
     */
    private String getExceptionMessageAsTitle(Throwable err) {
        if (err instanceof BadRequestAlertException badRequestException) {
            ProblemDetailWithCause innerProblem = badRequestException.getProblemDetailWithCause();
            if (innerProblem != null && innerProblem.getTitle() != null) {
                return replaceTile(innerProblem.getTitle());
            }
        }

        // 特殊处理Jakarta Bean Validation异常，提取验证注解中的message
        String validationMessage = extractValidationMessage(err);
        if (validationMessage != null) {
            return replaceTile(validationMessage);
        }

        // 对于所有其他异常，提取异常消息作为title
        String message = err.getMessage();
        if (StringUtils.isNotBlank(message) && !containsPackageName(message)) {
            return replaceTile(message);
        }

        // 如果异常本身的消息为空或包含包名，尝试获取cause的消息
        Throwable cause = err.getCause();
        if (cause != null) {
            String causeMessage = cause.getMessage();
            if (StringUtils.isNotBlank(causeMessage) && !containsPackageName(causeMessage)) {
                return replaceTile(causeMessage);
            }
        }

        // 如果都没有合适的消息，返回null
        return null;
    }

    /**
     * 提取校验异常中的message信息
     * 解析Bean Validation注解中的自定义message，如@NotNull(message = "字段不能为空")
     *
     * @param err 异常对象
     * @return 校验注解中的message，如果没有则返回null
     */
    private String extractValidationMessage(Throwable err) {
        // 处理WebExchangeBindException（WebFlux中@Valid注解触发的校验异常）
        if (err instanceof WebExchangeBindException bindException) {
            List<FieldError> fieldErrors = bindException.getBindingResult().getFieldErrors();
            if (!fieldErrors.isEmpty()) {
                // 获取第一个字段错误的默认消息（通常是注解中的message属性）
                FieldError firstError = fieldErrors.get(0);
                String defaultMessage = firstError.getDefaultMessage();

                // 如果defaultMessage不是以{开头的国际化key，则直接使用
                if (StringUtils.isNotBlank(defaultMessage) && !defaultMessage.startsWith("{")) {
                    // 对于单个字段错误，直接返回消息
                    if (fieldErrors.size() == 1) {
                        return defaultMessage;
                    } else {
                        // 多个字段错误时，显示第一个并提示还有其他错误
                        String fieldName = firstError.getField();
                        return String.format("字段 '%s' %s等%d个字段校验失败", fieldName, defaultMessage, fieldErrors.size());
                    }
                } else {
                    // 对于国际化key，显示字段名和描述
                    String fieldName = firstError.getField();
                    if (fieldErrors.size() == 1) {
                        return String.format(
                            "字段 '%s' %s",
                            fieldName,
                            StringUtils.isNotBlank(defaultMessage) ? defaultMessage : "校验失败"
                        );
                    } else {
                        return String.format(
                            "字段 '%s' %s等%d个字段校验失败",
                            fieldName,
                            StringUtils.isNotBlank(defaultMessage) ? defaultMessage : "校验失败",
                            fieldErrors.size()
                        );
                    }
                }
            }
        }

        // 处理ConstraintViolationException（@Validated注解触发的校验异常）
        if (err instanceof ConstraintViolationException constraintException) {
            Set<ConstraintViolation<?>> violations = constraintException.getConstraintViolations();
            if (!violations.isEmpty()) {
                // 获取第一个约束违反的消息
                ConstraintViolation<?> firstViolation = violations.iterator().next();
                String message = firstViolation.getMessage();

                // 如果message不是以{开头的国际化key，则直接使用
                if (StringUtils.isNotBlank(message) && !message.startsWith("{")) {
                    // 对于单个约束违反，直接返回消息
                    if (violations.size() == 1) {
                        return message;
                    } else {
                        // 多个约束违反时，显示第一个并提示还有其他错误
                        String propertyPath = firstViolation.getPropertyPath().toString();
                        return String.format("字段 '%s' %s等%d个字段校验失败", propertyPath, message, violations.size());
                    }
                } else {
                    // 对于国际化key，显示字段名和描述
                    String propertyPath = firstViolation.getPropertyPath().toString();
                    if (violations.size() == 1) {
                        return String.format("字段 '%s' %s", propertyPath, StringUtils.isNotBlank(message) ? message : "校验失败");
                    } else {
                        return String.format(
                            "字段 '%s' %s等%d个字段校验失败",
                            propertyPath,
                            StringUtils.isNotBlank(message) ? message : "校验失败",
                            violations.size()
                        );
                    }
                }
            }
        }

        return null;
    }

    /**
     * 获取详细的错误信息，用于detail字段
     * 使用原生异常信息，提供比title更详细的错误描述
     */
    private String getDetailedErrorInfo(Throwable err, int statusCode) {
        // 特殊异常类型的详细信息
        if (err instanceof BadRequestAlertException badRequestException) {
            ProblemDetailWithCause innerProblem = badRequestException.getProblemDetailWithCause();
            if (innerProblem != null && innerProblem.getDetail() != null) {
                return innerProblem.getDetail();
            }
            // 使用原生异常信息
            return getOriginalExceptionDetail(err);
        }

        if (err instanceof WebExchangeBindException bindException) {
            // 获取字段验证错误的详细信息
            StringBuilder details = new StringBuilder();
            bindException
                .getBindingResult()
                .getFieldErrors()
                .forEach(fieldError -> {
                    if (!details.isEmpty()) {
                        details.append("; ");
                    }
                    details.append("Field '").append(fieldError.getField()).append("': ").append(fieldError.getDefaultMessage());
                });
            return !details.isEmpty() ? details.toString() : getOriginalExceptionDetail(err);
        }

        if (err instanceof ConstraintViolationException constraintException) {
            // 获取约束违反的详细信息
            StringBuilder details = new StringBuilder();
            constraintException
                .getConstraintViolations()
                .forEach(violation -> {
                    if (!details.isEmpty()) {
                        details.append("; ");
                    }
                    details.append("Property '").append(violation.getPropertyPath()).append("': ").append(violation.getMessage());
                });
            return !details.isEmpty() ? details.toString() : getOriginalExceptionDetail(err);
        }

        // 对于其他异常类型，直接使用原生异常信息
        return getOriginalExceptionDetail(err);
    }

    /**
     * 获取原生异常的详细信息
     */
    private String getOriginalExceptionDetail(Throwable err) {
        StringBuilder detail = new StringBuilder();

        // 添加异常类型信息
        detail.append("Exception: ").append(err.getClass().getSimpleName());

        // 添加异常消息
        if (err.getMessage() != null && !err.getMessage().trim().isEmpty()) {
            detail.append(", Message: ").append(err.getMessage());
        }

        // 添加cause信息
        if (err.getCause() != null) {
            detail.append(", Cause: ").append(err.getCause().getClass().getSimpleName());
            if (err.getCause().getMessage() != null && !err.getCause().getMessage().trim().isEmpty()) {
                detail.append(" - ").append(err.getCause().getMessage());
            }
        }

        return detail.toString();
    }

    private HttpStatus getMappedStatus(Throwable err) {
        // Where we disagree with Spring defaults
        if (err instanceof AccessDeniedException) return HttpStatus.FORBIDDEN;
        if (err instanceof ConcurrencyFailureException) return HttpStatus.CONFLICT;
        if (err instanceof BadCredentialsException) return HttpStatus.UNAUTHORIZED;
        if (err instanceof ConstraintViolationException) return HttpStatus.BAD_REQUEST;
        return null;
    }

    private URI getPathValue(ServerRequest request) {
        if (request == null) return URI.create("about:blank");
        return URI.create(extractURI(request));
    }

    private Map<String, Object> buildAdditionalProperties(Throwable err) {
        Map<String, Object> additionalProperties = new HashMap<>();
        if (err instanceof BadRequestAlertException badRequestAlertException) {
            additionalProperties.put("entityName", badRequestAlertException.getEntityName());
            additionalProperties.put("errorKey", badRequestAlertException.getErrorKey());
        }
        return additionalProperties;
    }

    public Optional<ProblemDetailWithCause> buildCause(final Throwable throwable, ServerRequest request) {
        if (throwable != null && isCasualChainEnabled()) {
            return Optional.of(customizeProblem(getProblemDetailWithCause(throwable), throwable, request));
        }
        return Optional.empty();
    }

    private boolean isCasualChainEnabled() {
        return CASUAL_CHAIN_ENABLED;
    }

    private boolean containsPackageName(String message) {
        return StringUtils.containsAny(
            message,
            "org.",
            "java.",
            "net.",
            "jakarta.",
            "javax.",
            "com.",
            "io.",
            "de.",
            "com.whiskerguard.auth"
        );
    }

    private String replaceTile(String title) {
        if ("Bad Request".equals(title)) {
            return "请求参数错误";
        } else if ("Internal Server Error".equals(title)) {
            return "系统繁忙，请稍后再试";
        } else if ("Not Found".equals(title)) {
            return "系统正常初始化，请稍后再试";
        } else {
            return title;
        }
    }
}
