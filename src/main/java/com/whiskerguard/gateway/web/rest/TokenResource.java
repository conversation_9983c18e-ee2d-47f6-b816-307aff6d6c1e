package com.whiskerguard.gateway.web.rest;

import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.web.bind.annotation.*;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/api")
public class TokenResource {

    private final Logger log = LoggerFactory.getLogger(TokenResource.class);
    private final ReactiveJwtDecoder jwtDecoder;

    @Value("${jhipster.security.authentication.jwt.base64-secret}")
    private String jwtSecret;

    public TokenResource(ReactiveJwtDecoder jwtDecoder) {
        this.jwtDecoder = jwtDecoder;
    }

    @PostMapping(value = "/auth/introspect", produces = MediaType.APPLICATION_JSON_VALUE)
    public Mono<ResponseEntity<Map<String, Object>>> introspectToken(@RequestBody Map<String, String> body) {
        log.info("REST request to introspect token");
        String token = body.get("token");

        if (token == null || token.isEmpty()) {
            log.info("No token provided");
            return Mono.just(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(createInactiveResponse()));
        }

        return jwtDecoder
            .decode(token)
            .map(jwt -> {
                Map<String, Object> response = new HashMap<>();
                response.put("active", true);
                response.put("sub", jwt.getSubject());
                response.put("exp", jwt.getExpiresAt());
                response.put("iat", jwt.getIssuedAt());
                response.put("tenant_id", jwt.getClaimAsString("tenant_id"));
                log.info("Token is active, returning response: {}", response);
                return ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(response);
            })
            .onErrorResume(e -> {
                log.error("Token validation failed", e);
                return Mono.just(ResponseEntity.ok().contentType(MediaType.APPLICATION_JSON).body(createInactiveResponse()));
            });
    }

    private Map<String, Object> createInactiveResponse() {
        Map<String, Object> response = new HashMap<>();
        response.put("active", false);
        return response;
    }
}
