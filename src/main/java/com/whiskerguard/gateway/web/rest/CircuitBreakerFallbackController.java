package com.whiskerguard.gateway.web.rest;

import java.util.HashMap;
import java.util.Map;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import reactor.core.publisher.Mono;

@RestController
@RequestMapping("/fallback")
public class CircuitBreakerFallbackController {

    @GetMapping("/global")
    public Mono<ResponseEntity<Map<String, Object>>> globalFallback() {
        return createFallbackResponse("Global service is currently unavailable");
    }

    @GetMapping("/tenant")
    public Mono<ResponseEntity<Map<String, Object>>> tenantFallback() {
        return createFallbackResponse("Tenant service is currently unavailable");
    }

    @GetMapping("/user")
    public Mono<ResponseEntity<Map<String, Object>>> userFallback() {
        return createFallbackResponse("User service is currently unavailable");
    }

    private Mono<ResponseEntity<Map<String, Object>>> createFallbackResponse(String message) {
        Map<String, Object> response = new HashMap<>();
        response.put("status", "SERVICE_UNAVAILABLE");
        response.put("message", message);
        response.put("timestamp", System.currentTimeMillis());

        return Mono.just(ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE).body(response));
    }
}
