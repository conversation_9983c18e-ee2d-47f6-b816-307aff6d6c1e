# ===================================================================
# Spring Boot configuration for the "dev" profile.
#
# This configuration overrides the application.yml file.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

logging:
  level:
    ROOT: INFO
    tech.jhipster: DEBUG
    org.hibernate.SQL: DEBUG
    com.whiskerguard.gateway: DEBUG
    org.springframework.web: INFO
    org.springframework.security: INFO
    org.springframework.cloud.gateway: INFO

management:
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: true
      additional-exclude: static/**
    livereload:
      enabled: false # we use Webpack dev server + BrowserSync for livereload
  jackson:
    serialization:
      indent-output: true
  cloud:
    consul:
      config:
        fail-fast: false # if not in "prod" profile, do not force to use Spring Cloud Config
        format: yaml
        profile-separator: '-'
      discovery:
        prefer-ip-address: true
        tags:
          - profile=${spring.profiles.active}
          - version='@project.version@'
          - git-version=${git.commit.id.describe:}
          - git-commit=${git.commit.id.abbrev:}
          - git-branch=${git.branch:}
      host: localhost
      port: 8500
  liquibase:
    # Remove 'faker' if you do not want the sample data to be loaded automatically
    contexts: dev, faker
    url: ********************************************************************************************************************************************************
    enabled: false
  messages:
    cache-duration: PT1S # 1 second, see the ISO 8601 standard
  r2dbc:
    url: r2dbc:mysql://dev.mysql.mbbhg.com:3306/maobobo_gateway?useUnicode=true&characterEncoding=utf8&useSSL=false&useLegacyDatetimeCode=false&createDatabaseIfNotExist=true&allowPublicKeyRetrieval=true
    username: root
    password: MBB_root_2025@
  thymeleaf:
    cache: false
  data:
    redis:
      host: dev.redis.mbbhg.com
      port: 6379
      database: 0
      timeout: 2000
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1

server:
  port: 8080
  # make sure requests the proxy uri instead of the server one
  forward-headers-strategy: native

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  gateway:
    rate-limiting:
      enabled: false
      limit: 100000
      duration-in-seconds: 3600
  # CORS is only enabled by default with the "dev" profile
  cors:
    # Allow Ionic for JHipster by default (* no longer allowed in Spring Boot 2.4+)
    allowed-origins: 'http://localhost:8100,https://localhost:8100'
    # Enable CORS when running in GitHub Codespaces
    allowed-origin-patterns: 'https://*.githubpreview.dev'
    allowed-methods: '*'
    allowed-headers: '*'
    exposed-headers: 'Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params'
    allow-credentials: true
    max-age: 1800
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 and be at least 256 bits long (you can type `openssl rand -base64 64` on your command line to generate a 512 bits one)
        base64-secret: ZGRiYjA3MjVmMzM2YzY3MzRhYWYxYWNhYTUxNDE5YWQ0NmZmZGI4MTVlYWYzNjg5YmY2NWE0MGU1NTc4NjMzYzkxMDI0ZTlhZjNkMTBkMjk5ZjA5ODE0OThiOTMzZDk4ZGM1NjIwNDkxNzA1YmQzMWNhYzQxNmEyM2VjMTc2NDE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
gateway:
  ip-access-control:
    whitelist-mode: false
    whitelist:
      - '127.0.0.1/32'
      - '127.0.0.1/8'
      - '::1/128'
    blacklist:
      - '10.0.0.0/8'
    sensitive-paths:
      - '/api/admin/.*'
      - '/api/internal/.*'
      - '/api/management/.*'
      - '/api/sensitive/.*'

# ===================================================================
# License权限验证配置
# ===================================================================
license:
  permission:
    enabled: true
    fallback-allow: true # 开发环境允许降级访问
    path-mappings:
      # 三张清单AI生成功能配置
      - path: '/api/compliance/risk/list/ai/**'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '三张清单AI生成功能 - 所有AI相关的合规风险清单接口'
    cache:
      enabled: true
      ttl: 300 # 5分钟缓存
      max-size: 1000
    circuit-breaker:
      failure-rate-threshold: 50
      wait-duration-in-open-state: 30
      permitted-number-of-calls-in-half-open-state: 10
      sliding-window-size: 100

# ===================================================================
# ReactiveFeignClient配置
# ===================================================================
reactive:
  feign:
    client:
      config:
        whiskerguardlicenseservice:
          connect-timeout: 10000 # 连接超时10秒
          read-timeout: 30000 # 读取超时30秒（增加超时时间）
          write-timeout: 30000 # 写入超时30秒
          logger-level: FULL # 开发环境启用详细日志
    circuit:
      breaker:
        enabled: true
        whiskerguardlicenseservice:
          failure-rate-threshold: 80 # 失败率阈值提高到80%
          wait-duration-in-open-state: 60s # 熔断器打开状态持续时间
          permitted-number-of-calls-in-half-open-state: 5 # 半开状态允许的调用数
          sliding-window-size: 20 # 滑动窗口大小减小
          minimum-number-of-calls: 10 # 最小调用数才开始计算失败率
    retry:
      whiskerguardlicenseservice:
        max-attempts: 3
        initial-interval: 1000
        max-interval: 5000
        multiplier: 2.0
