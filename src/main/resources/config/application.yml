# ===================================================================
# Spring Boot configuration.
#
# This configuration will be overridden by the Spring profile you use,
# for example application-dev.yml if you use the "dev" profile.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

# Conditionally disable springdoc on missing api-docs profile
spring:
  config:
    activate:
      on-profile: '!api-docs'
springdoc:
  api-docs:
    enabled: false
  show-actuator: true
  swagger-ui:
    enabled: true
    path: /swagger-ui.html
---
reactive:
  feign:
    circuit:
      breaker:
        enabled: true

management:
  endpoints:
    web:
      base-path: /management
      exposure:
        include:
          - configprops
          - env
          - gateway
          - health
          - info
          - jhimetrics
          - jhiopenapigroups
          - logfile
          - loggers
          - prometheus
          - threaddump
          - liquibase
  endpoint:
    health:
      show-details: when-authorized
      roles: 'ROLE_ADMIN'
      probes:
        enabled: true
      group:
        liveness:
          include: livenessState,ping
        readiness:
          include: readinessState,redis
    jhimetrics:
      enabled: true
  info:
    git:
      mode: full
    env:
      enabled: true
  health:
    mail:
      enabled: false # When using the MailService, configure an SMTP server and set this to true
  prometheus:
    metrics:
      export:
        enabled: true
        step: 60
  observations:
    key-values:
      application: ${spring.application.name}
  metrics:
    enable:
      http: true
      jvm: true
      logback: true
      process: true
      system: true
    distribution:
      percentiles-histogram:
        all: true
      percentiles:
        all: 0, 0.5, 0.75, 0.95, 0.99, 1.0
    data:
      repository:
        autotime:
          enabled: true
    tags:
      application: ${spring.application.name}

spring:
  application:
    name: gatewayService
  cloud:
    consul:
      discovery:
        healthCheckPath: /management/health
        healthCheckInterval: 10s
        healthCheckTimeout: 5s
        healthCheckCriticalTimeout: 30s
        deregister: true
        instanceId: gatewayservice:${spring.application.instance-id:${random.value}}
        service-name: gatewayservice
      config:
        watch:
          enabled: true
    gateway:
      routes:
        - id: auth-oauth-token
          uri: lb://whiskerguardauthservice
          predicates:
            - Path=/api/oauth/token
          filters: [ ]

      default-filters:
        - JWTRelay
        - UserInfo
      discovery:
        locator:
          enabled: true
          lower-case-service-id: true
          predicates:
            - name: Path
              args:
                pattern: "'/services/'+serviceId.toLowerCase()+'/**'"
          filters:
            - StripPrefix=2
      httpclient:
        pool:
          max-connections: 1000
  docker:
    compose:
      enabled: false
      lifecycle-management: start-only
      file: src/main/docker/services.yml
  profiles:
    # The commented value for `active` can be replaced with valid Spring profiles to load.
    # Otherwise, it will be filled in by maven when building the JAR file
    # Either way, it can be overridden by `--spring.profiles.active` value passed in the commandline or `-Dspring.profiles.active` set in `JAVA_OPTS`
    active: '@spring.profiles.active@'
    group:
      dev:
        - dev
        - api-docs
        # Uncomment to activate TLS for the dev profile
        #- tls
  jmx:
    enabled: false
  messages:
    basename: i18n/messages
  main:
    allow-bean-definition-overriding: true
  webflux:
    problemdetails:
      enabled: true
  security:
    user:
      name: admin
      password: admin
      roles:
        - ADMIN
        - USER
    oauth2:
      resourceserver:
        jwt:
          authority-prefix: ''
          authorities-claim-name: auth
  task:
    execution:
      thread-name-prefix: gateway-service-task-
      pool:
        core-size: 2
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: gateway-service-scheduling-
      pool:
        size: 2
  thymeleaf:
    mode: HTML
  output:
    ansi:
      console-available: true
  cache:
    type: redis
    redis:
      time-to-live: 600000 # 10分钟
      cache-null-values: false
      key-prefix: cache
server:
  servlet:
    session:
      cookie:
        http-only: true

# Properties to be exposed on the /info management endpoint
info:
  # Comma separated list of profiles that will trigger the ribbon to show
  display-ribbon-on-profiles: 'dev'

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================

jhipster:
  clientApp:
    name: 'gatewayServiceApp'
  # By default CORS is disabled. Uncomment to enable.
  # cors:
  #   allowed-origins: "http://localhost:8100,http://localhost:9000"
  #   allowed-methods: "*"
  #   allowed-headers: "*"
  #   exposed-headers: "Authorization,Link,X-Total-Count,X-${jhipster.clientApp.name}-alert,X-${jhipster.clientApp.name}-error,X-${jhipster.clientApp.name}-params"
  #   allow-credentials: true
  #   max-age: 1800
  mail:
    from: gatewayService@localhost
  api-docs:
    default-include-pattern: /api/**
    management-include-pattern: /management/**
    title: Gateway Service API
    description: Gateway Service API documentation
    version: 0.0.1
    terms-of-service-url:
    contact-name:
    contact-url:
    contact-email:
    license: unlicensed
    license-url:
  security:

# jhipster-needle-add-application-yaml-document
---
# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:

rate-limiter:
  rules:
    global:
      replenishRate: 100
      burstCapacity: 100
      type: GLOBAL
      priority: 1
      timeWindow: 1
      algorithm: TOKEN_BUCKET
      enableRetry: true
      maxRetryAttempts: 3
      retryBackoffMultiplier: 2.0
      enableLocalFallback: true
      enableDetailedLogging: false
      customErrorMessage: "Global rate limit exceeded. Please try again later."
      enableHealthCheck: true
      healthCheckInterval: 30
      failureThreshold: 0.5
      pathPattern: "/**"
      excludePaths: [ "/actuator/**", "/health/**" ]
      includeMethods: [ "GET", "POST", "PUT", "DELETE", "PATCH" ]
    ip:
      replenishRate: 50
      burstCapacity: 50
      type: IP
      priority: 2
      timeWindow: 1
      algorithm: TOKEN_BUCKET
      enableRetry: true
      maxRetryAttempts: 2
      retryBackoffMultiplier: 1.5
      enableLocalFallback: true
      enableDetailedLogging: false
      customErrorMessage: "IP rate limit exceeded. Please try again later."
      enableHealthCheck: true
      healthCheckInterval: 30
      failureThreshold: 0.6
      pathPattern: "/api/**"
      excludePaths: [ "/api/health/**" ]
      includeMethods: [ "GET", "POST", "PUT", "DELETE", "PATCH" ]
    user:
      replenishRate: 20
      burstCapacity: 20
      type: USER
      priority: 3
      timeWindow: 1
      algorithm: SLIDING_WINDOW
      enableRetry: false
      maxRetryAttempts: 1
      retryBackoffMultiplier: 1.0
      enableLocalFallback: true
      enableDetailedLogging: true
      customErrorMessage: "User rate limit exceeded. Please try again later."
      enableHealthCheck: true
      healthCheckInterval: 60
      failureThreshold: 0.3
      pathPattern: "/api/employees/**"
      excludePaths: [ ]
      includeMethods: [ "GET", "POST", "PUT", "DELETE" ]
    tenant:
      replenishRate: 200
      burstCapacity: 200
      type: TENANT
      priority: 4
      timeWindow: 1
      algorithm: TOKEN_BUCKET
      enableRetry: true
      maxRetryAttempts: 5
      retryBackoffMultiplier: 2.5
      enableLocalFallback: true
      enableDetailedLogging: false
      customErrorMessage: "Tenant rate limit exceeded. Please try again later."
      enableHealthCheck: true
      healthCheckInterval: 15
      failureThreshold: 0.7
      pathPattern: "/api/tenant/**"
      excludePaths: [ "/api/tenant/health/**" ]
      includeMethods: [ "GET", "POST", "PUT", "DELETE", "PATCH" ]

retry:
  default-config:
    max-attempts: 3
    initial-interval: 1000
    max-interval: 10000
    multiplier: 2.0
    jitter: 0.1
    retryable-statuses: [ 'BAD_GATEWAY', 'SERVICE_UNAVAILABLE', 'GATEWAY_TIMEOUT' ]
    retryable-methods: [ 'GET', 'POST' ]
  services:
    auth-service:
      service-name: whiskerguard-auth-service
      enabled: true
      max-attempts: 3
      initial-interval: 1000
      max-interval: 5000
      multiplier: 2.0
      jitter: 0.1
      retryable-statuses: [ 'BAD_GATEWAY', 'SERVICE_UNAVAILABLE', 'GATEWAY_TIMEOUT' ]
      retryable-methods: [ 'GET', 'POST' ]
    user-service:
      service-name: user-service
      enabled: true
      max-attempts: 2
      initial-interval: 500
      max-interval: 3000
      multiplier: 1.5
      jitter: 0.1
      retryable-statuses: [ 'BAD_GATEWAY', 'SERVICE_UNAVAILABLE' ]
      retryable-methods: [ 'GET' ]

# Consul 路由配置
consul:
  route:
    enabled: true
    route-key: gateway/routes
    watch-enabled: true
    watch-delay: 1000
    skip-urls:
      - '/login'
      - '/callback'
      - '/password/reset'
      - '/verification/code/send'
      - 'MP_verify_pMjb7EmECeca3bYh.txt'
      - '/api/reservations'

# 日志配置
logging:
  level:
    com.whiskerguard.gateway: INFO
    org.springframework.cloud.gateway: INFO
    org.springframework.http.server.reactive: INFO
    org.springframework.web.reactive: INFO
    reactor.netty: INFO
    redisratelimiter: INFO


gateway:
  ip-access-control:
    whitelist-mode: false
    whitelist:
      - '127.0.0.1/32'
      - '127.0.0.1/8'
      - '::1/128'
    blacklist:
      - '10.0.0.0/8'
    sensitive-paths:
      - '/api/admin/.*'
      - '/api/internal/.*'
      - '/api/management/.*'
      - '/api/sensitive/.*'
