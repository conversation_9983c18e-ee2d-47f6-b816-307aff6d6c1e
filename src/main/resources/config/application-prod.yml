logging:
  level:
    ROOT: INFO
    tech.jhipster: INFO
    org.springframework.security: INFO
    com.whiskerguard.gateway: DEBUG

management:
  prometheus:
    metrics:
      export:
        enabled: false
  zipkin: # Use the "zipkin" Maven profile to have the Spring Cloud Zipkin dependencies
    tracing:
      endpoint: http://localhost:9411/api/v2/spans
  tracing:
    sampling:
      probability: 1.0 # report 100% of traces

spring:
  devtools:
    restart:
      enabled: false
    livereload:
      enabled: false
  cloud:
    consul:
      discovery:
        prefer-ip-address: true
        ip-address: **********
      host: ***********
      port: 8500
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: prod
    url: ********************************************************************************************************************************************************
    enabled: false
  r2dbc:
    url: r2dbc:mysql://***********:3306/whiskerguard-auth-service-db?useUnicode=true&characterEncoding=utf8&useSSL=false&useLegacyDatetimeCode=false&createDatabaseIfNotExist=true&allowPublicKeyRetrieval=true
    username: wg-auth-service-user
    password: P@sr9uZ8mT!4fQ2wNb6X
  thymeleaf:
    cache: true
  data:
    redis:
      host: ***********
      port: 6379
      database: 0
      timeout: 2000
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
          min-idle: 0
          max-wait: -1

server:
  port: 8080
  shutdown: graceful # see https://docs.spring.io/spring-boot/docs/current/reference/html/spring-boot-features.html#boot-features-graceful-shutdown
  compression:
    enabled: true
    mime-types: text/html,text/xml,text/plain,text/css,application/javascript,application/json,image/svg+xml
    min-response-size: 1024

jhipster:
  http:
    cache: # Used by the CachingHttpHeadersFilter
      timeToLiveInDays: 1461
  security:
    authentication:
      jwt:
        base64-secret: ZGRiYjA3MjVmMzM2YzY3MzRhYWYxYWNhYTUxNDE5YWQ0NmZmZGI4MTVlYWYzNjg5YmY2NWE0MGU1NTc4NjMzYzkxMDI0ZTlhZjNkMTBkMjk5ZjA5ODE0OThiOTMzZDk4ZGM1NjIwNDkxNzA1YmQzMWNhYzQxNmEyM2VjMTc2NDE=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 2592000
  logging:
    use-json-format: false # By default, logs are not in Json format
    logstash: # Forward logs to logstash over a socket, used by LoggingConfiguration
      enabled: true
      host: ***********
      port: 5000
      ring-buffer-size: 512

# ===================================================================
# License权限验证配置
# ===================================================================
license:
  permission:
    enabled: true
    fallback-allow: false # 测试环境拒绝访问
    path-mappings:
      # 三张清单AI生成功能配置
      - path: '/api/compliance/risk/list/ai/**'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '风险识别清单AI生成功能 - 所有AI相关的合规风险清单接口'
      - path: '/api/compliance/risk/list/model/analysis'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '风险识别清单AI生成功能 - 智能分析风险等级模型'
      - path: '/api/duty/positions/parse/job/description'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '重点岗位职责清单AI生成功能 - AI解析岗位说明书'
      - path: '/api/duty/positions/generate/compliance/requirements'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '重点岗位职责清单AI生成功能 - AI生成合规要求'
      - path: '/api/duty/positions/generate/ai/**'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '重点岗位职责清单AI生成功能 - AI分析八项权力并生成防控措施以及综合分析'
      - path: '/api/v2/biz/process/ai/**'
        feature: 'AI_THREE_LIST_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '关键流程管控清单AI生成功能 - AI分析业务流程管控清单'
      - path: '/api/exam/management/start'
        feature: 'COURSE_EXAM_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '考试相关功能 - 开始考试'
      - path: '/api/course/chapters/course/**'
        feature: 'COURSE_EXAM_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '课程章节相关功能 - 根据课程ID获取所有章节信息，包含课时和学习进度'
      - path: '/api/enterprise/regulation/audits/ai/review/**'
        feature: 'INTERNAL_AUDIT_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/laws/regulation/transforms/audit/**'
        feature: 'REGULATION_SYNC_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/task/statistics/dashboard'
        feature: 'COMPLIANCE_DASHBOARD_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/task/risk/distribution'
        feature: 'COMPLIANCE_DASHBOARD_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/task/trend'
        feature: 'COMPLIANCE_DASHBOARD_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/contract/message/review/ai/review/**'
        feature: 'CONTRACT_RISK_SCAN_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/compliance/reviews/ai/review'
        feature: 'MAJOR_DECISION_REVIEW_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/verification/code/send'
        feature: 'SMS_PUSH_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/verification/code/batch/send'
        feature: 'SMS_PUSH_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
      - path: '/api/ai/invoke'
        feature: 'LLM_QA_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '智能问答相关功能 - AI生成'
      - path: '/api/ai/stream'
        feature: 'LLM_QA_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '智能问答相关功能 - AI流式调用'
      - path: '/api/contract/smes'
        feature: 'CONTRACT_DRAFTING_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '中小企业合同管理功能 - 合同起草'
      - path: '/api/contract/review/parse/document'
        feature: 'CONTRACT_REVIEW_FEATURE'
        methods: ['*'] # 支持所有HTTP方法
        description: '中小企业合同管理功能 - 合同审查'
    cache:
      enabled: true
      ttl: 300 # 5分钟缓存
      max-size: 1000
    circuit-breaker:
      failure-rate-threshold: 50
      wait-duration-in-open-state: 30
      permitted-number-of-calls-in-half-open-state: 10
      sliding-window-size: 100

# ===================================================================
# ReactiveFeignClient配置
# ===================================================================
reactive:
  feign:
    client:
      config:
        whiskerguardlicenseservice:
          connect-timeout: 5000 # 连接超时5秒
          read-timeout: 10000 # 读取超时10秒
          write-timeout: 10000 # 写入超时10秒
          logger-level: FULL # 开发环境启用详细日志
    circuit:
      breaker:
        enabled: true
        whiskerguardlicenseservice:
          failure-rate-threshold: 50
          wait-duration-in-open-state: 30s
          permitted-number-of-calls-in-half-open-state: 10
          sliding-window-size: 100
    retry:
      whiskerguardlicenseservice:
        max-attempts: 3
        initial-interval: 1000
        max-interval: 5000
        multiplier: 2.0
