package com.whiskerguard.gateway.security.jwt;

import com.whiskerguard.gateway.config.TestConsulConfiguration;
import com.whiskerguard.gateway.config.TestSecurityConfig;
import com.whiskerguard.gateway.web.rest.TokenResource;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;

@WebFluxTest(controllers = TokenResource.class)
@Import({ TestSecurityConfig.class, TestConsulConfiguration.class })
@ActiveProfiles("test")
class AuthenticationEndpointTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private ReactiveJwtDecoder reactiveJwtDecoder;
    // @Test
    // void testAuthenticationEndpoint() {
    //     webTestClient
    //         .post()
    //         .uri("/api/auth/token")
    //         .contentType(MediaType.APPLICATION_JSON)
    //         .exchange()
    //         .expectStatus()
    //         .isOk()
    //         .expectBody()
    //         .jsonPath("$.id_token")
    //         .exists();
    // }
}
