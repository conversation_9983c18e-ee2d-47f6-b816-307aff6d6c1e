package com.whiskerguard.gateway.config;

import com.ecwid.consul.v1.ConsulClient;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;

@TestConfiguration
public class TestConsulConfig {

    @Bean
    @Primary
    public ConsulClient consulClient() {
        return Mockito.mock(ConsulClient.class);
    }
}
