package com.whiskerguard.gateway.config;


import java.util.Base64;
import javax.crypto.spec.SecretKeySpec;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.security.config.annotation.web.reactive.EnableWebFluxSecurity;
import org.springframework.security.config.web.server.ServerHttpSecurity;
import org.springframework.security.oauth2.jwt.NimbusReactiveJwtDecoder;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.security.web.server.SecurityWebFilterChain;
import org.springframework.security.web.server.context.ServerSecurityContextRepository;
import org.springframework.security.web.server.context.WebSessionServerSecurityContextRepository;
import org.springframework.web.cors.CorsConfiguration;
import tech.jhipster.config.JHipsterProperties;

@TestConfiguration
@EnableWebFluxSecurity
public class TestSecurityConfig {

    @Value("${jhipster.security.authentication.jwt.base64-secret:test-secret-key-that-is-long-enough-for-testing-purposes-only}")
    private String jwtSecret;

    @Value("${jhipster.security.authentication.jwt.token-validity-in-seconds:3600}")
    private long tokenValidityInSeconds;

    @Bean
    public JHipsterProperties jHipsterProperties() {
        JHipsterProperties properties = new JHipsterProperties();

        // Configure CORS
        CorsConfiguration corsConfiguration = new CorsConfiguration();
        corsConfiguration.addAllowedOrigin("*");
        corsConfiguration.addAllowedHeader("*");
        corsConfiguration.addAllowedMethod("*");
        properties.getCors().setAllowedOrigins(corsConfiguration.getAllowedOrigins());
        properties.getCors().setAllowedHeaders(corsConfiguration.getAllowedHeaders());
        properties.getCors().setAllowedMethods(corsConfiguration.getAllowedMethods());

        // Configure JWT
        properties.getSecurity().getAuthentication().getJwt().setBase64Secret(jwtSecret);
        properties.getSecurity().getAuthentication().getJwt().setTokenValidityInSeconds(tokenValidityInSeconds);

        // Configure client app
        properties.getClientApp().setName("gatewayServiceApp");

        // Configure mail
        properties.getMail().setFrom("gatewayService@localhost");

        // Configure API docs
        properties.getApiDocs().setDefaultIncludePattern(new String[] { "/api/**" });
        properties.getApiDocs().setManagementIncludePattern(new String[] { "/management/**" });
        properties.getApiDocs().setTitle("Gateway Service API");
        properties.getApiDocs().setDescription("Gateway Service API documentation");
        properties.getApiDocs().setVersion("0.0.1");

        return properties;
    }

    @Bean
    public SecurityWebFilterChain springSecurityFilterChain(ServerHttpSecurity http) {
        http
            .csrf()
            .disable()
            .authorizeExchange()
            .pathMatchers("/api/auth/**", "/api/test/**")
            .permitAll()
            .anyExchange()
            .authenticated()
            .and()
            .oauth2ResourceServer()
            .jwt();
        return http.build();
    }

    @Bean
    public ServerSecurityContextRepository securityContextRepository() {
        return new WebSessionServerSecurityContextRepository();
    }

    @Bean
    public ReactiveJwtDecoder reactiveJwtDecoder() {
        byte[] keyBytes = Base64.getDecoder().decode(jwtSecret);
        SecretKeySpec key = new SecretKeySpec(keyBytes, "HmacSHA512");
        return NimbusReactiveJwtDecoder.withSecretKey(key).build();
    }
}
