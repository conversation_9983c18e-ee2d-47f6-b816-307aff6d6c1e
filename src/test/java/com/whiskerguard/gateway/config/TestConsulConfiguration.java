package com.whiskerguard.gateway.config;

import com.ecwid.consul.v1.ConsulClient;
import org.mockito.Mockito;
import org.springframework.boot.test.context.TestConfiguration;
import org.springframework.cloud.client.discovery.DiscoveryClient;
import org.springframework.cloud.gateway.route.RouteLocator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Primary;
import org.springframework.web.reactive.function.client.WebClient;

@TestConfiguration
public class TestConsulConfiguration {

    @Bean
    @Primary
    public ConsulClient consulClient() {
        return Mockito.mock(ConsulClient.class);
    }

    @Bean
    @Primary
    public RouteLocator routeLocator() {
        return Mockito.mock(RouteLocator.class);
    }

    @Bean
    @Primary
    public DiscoveryClient discoveryClient() {
        return Mockito.mock(DiscoveryClient.class);
    }

    @Bean
    @Primary
    public WebClient.Builder webClientBuilder() {
        return WebClient.builder();
    }
}
