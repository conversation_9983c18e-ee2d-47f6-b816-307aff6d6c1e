package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.config.IpAccessControlProperties;
import java.util.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.http.HttpStatus;
import org.springframework.mock.http.server.reactive.MockServerHttpRequest;
import org.springframework.mock.web.server.MockServerWebExchange;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import reactor.test.StepVerifier;

class IpAccessControlFilterTest {

    private IpAccessControlFilter filter;

    @BeforeEach
    void setUp() {
        IpAccessControlProperties properties = new IpAccessControlProperties();
        properties.setWhitelistMode(true);
        properties.setWhitelist(Arrays.asList("127.0.0.1/32", "*************/32"));
        properties.setSensitivePaths(Arrays.asList("/api/admin/.*"));
        filter = new IpAccessControlFilter(properties);
    }

    @Test
    void shouldAllowWhitelistedIp() {
        ServerWebExchange exchange = MockServerWebExchange.from(
            MockServerHttpRequest.get("/api/test").header("X-Forwarded-For", "*************").build()
        );

        StepVerifier.create(filter.filter(exchange, exchange1 -> Mono.empty())).verifyComplete();
    }

    @Test
    void shouldBlockNonWhitelistedIp() {
        ServerWebExchange exchange = MockServerWebExchange.from(
            MockServerHttpRequest.get("/api/test").header("X-Forwarded-For", "*************").build()
        );

        filter.filter(exchange, exchange1 -> Mono.empty()).block();
        assert exchange.getResponse().getStatusCode() == HttpStatus.FORBIDDEN;
    }
}
