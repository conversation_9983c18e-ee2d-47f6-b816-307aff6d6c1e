package com.whiskerguard.gateway.filter;

import com.whiskerguard.gateway.GatewayServiceApp;
import com.whiskerguard.gateway.config.TestConsulConfig;
import com.whiskerguard.gateway.config.TestDatabaseConfig;
import com.whiskerguard.gateway.config.TestSecurityConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.AutoConfigureWebTestClient;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;
import tech.jhipster.config.JHipsterProperties;

@SpringBootTest(classes = GatewayServiceApp.class)
@AutoConfigureWebTestClient
@Import({ TestSecurityConfig.class, TestConsulConfig.class, TestDatabaseConfig.class })
@ActiveProfiles("test")
class UserInfoGatewayFilterTest {

    @Autowired
    private WebTestClient webTestClient;

    @Autowired
    private JHipsterProperties jHipsterProperties;


    // @Test
    // void testUserInfoInjectionWithoutAuth() {
    //     webTestClient
    //         .get()
    //         .uri("/api/test")
    //         .accept(MediaType.APPLICATION_JSON)
    //         .exchange()
    //         .expectStatus()
    //         .isOk()
    //         .expectHeader()
    //         .doesNotExist("X-User-Name")
    //         .expectHeader()
    //         .doesNotExist("X-User-Authorities");
    // }

    // @Test
    // @WithMockUser(username = "test-user", authorities = {"ROLE_USER"})
    // void testUserInfoInjectionWithAuth() {
    //     webTestClient
    //         .get()
    //         .uri("/api/test")
    //         .accept(MediaType.APPLICATION_JSON)
    //         .exchange()
    //         .expectStatus()
    //         .isOk()
    //         .expectHeader()
    //         .valueEquals("X-User-Name", "test-user")
    //         .expectHeader()
    //         .valueEquals("X-User-Authorities", "ROLE_USER");
    // }
}
