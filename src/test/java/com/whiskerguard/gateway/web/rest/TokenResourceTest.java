package com.whiskerguard.gateway.web.rest;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

import com.whiskerguard.gateway.config.TestSecurityConfig;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.reactive.WebFluxTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.http.MediaType;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.jwt.ReactiveJwtDecoder;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.web.reactive.server.WebTestClient;
import reactor.core.publisher.Mono;
import tech.jhipster.config.JHipsterProperties;

@WebFluxTest(controllers = TokenResource.class)
@Import(TestSecurityConfig.class)
@ActiveProfiles("test")
class TokenResourceTest {

    @Autowired
    private WebTestClient webTestClient;

    @MockBean
    private ReactiveJwtDecoder reactiveJwtDecoder;

    @Autowired
    private JHipsterProperties jHipsterProperties;

    private String validToken;
    private Jwt jwt;

    @BeforeEach
    void setUp() {
        // 与 TokenResource 保持一致的 base64 secret
        String base64Secret =
            "MWQyZThlMWEwYjcyYTczYWM1ZWU3ZjJmM2YyMzViNzRlNDkzZjJhNDRlOTg2NzhiZjE4NTEzMDk0M2EwY2FiNjI4NzdiMGE0YmIwMjc1MjRjZDk1MzhiYjJiNTkxOWQzNjBlM2NlMDZiZDliNDg3OGQzMjlkNDE5ZWM3OTIxNTg=";

        Map<String, Object> claims = new HashMap<>();
        claims.put("sub", "test-user");
        claims.put("authorities", Arrays.asList("ROLE_USER"));
        claims.put("tenant_id", "test-tenant");
        Instant now = Instant.now();
        claims.put("iat", now.getEpochSecond());
        claims.put("exp", now.plusSeconds(3600).getEpochSecond());
        validToken = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.xxxxx";
        jwt = Jwt.withTokenValue(validToken)
            .header("alg", "HS512")
            .claim("sub", "test-user")
            .claim("authorities", Arrays.asList("ROLE_USER"))
            .claim("tenant_id", "test-tenant")
            .issuedAt(now)
            .expiresAt(now.plusSeconds(3600))
            .build();

        when(reactiveJwtDecoder.decode(anyString())).thenReturn(Mono.just(jwt));
    }

    @Test
    void testIntrospectWithValidToken() {
        Map<String, String> body = new HashMap<>();
        body.put("token", validToken);

        webTestClient
            .post()
            .uri("/api/auth/introspect")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .exchange()
            .expectStatus()
            .isOk()
            .expectBody()
            .jsonPath("$.active")
            .isEqualTo(true)
            .jsonPath("$.sub")
            .isEqualTo("test-user");
    }

    @Test
    void testIntrospectWithoutToken() {
        Map<String, String> body = new HashMap<>();

        webTestClient
            .post()
            .uri("/api/auth/introspect")
            .contentType(MediaType.APPLICATION_JSON)
            .bodyValue(body)
            .exchange()
            .expectStatus()
            .isOk()
            .expectBody()
            .jsonPath("$.active")
            .isEqualTo(false);
    }
}
