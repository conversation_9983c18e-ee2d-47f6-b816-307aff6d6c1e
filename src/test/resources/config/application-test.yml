spring:
  r2dbc:
    url: r2dbc:h2:mem:///testdb;DB_CLOSE_DELAY=-1;MODE=MySQL
    username: sa
    password:
    pool:
      enabled: true
      initial-size: 5
      max-size: 10
      max-idle-time: 30m
  security:
    oauth2:
      client:
        provider:
          oidc:
            issuer-uri: http://localhost:9080/realms/jhipster
        registration:
          oidc:
            client-id: web_app
            client-secret: web_app
            scope: openid,profile,email
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        prefer-ip-address: true
        instance-id: ${spring.application.name}:${random.value}

jhipster:
  security:
    authentication:
      jwt:
        base64-secret: MWQyZThlMWEwYjcyYTczYWM1ZWU3ZjJmM2YyMzViNzRlNDkzZjJhNDRlOTg2NzhiZjE4NTEzMDk0M2EwY2FiNjI4NzdiMGE0YmIwMjc1MjRjZDk1MzhiYjJiNTkxOWQzNjBlM2NlMDZiZDliNDg3OGQzMjlkNDE5ZWM3OTIxNTg=
        token-validity-in-seconds: 3600
        token-validity-in-seconds-for-remember-me: 2592000
