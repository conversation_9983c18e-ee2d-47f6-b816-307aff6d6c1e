# ===================================================================
# Spring Boot configuration.
#
# This configuration is used for unit/integration tests.
#
# More information on profiles: https://www.jhipster.tech/profiles/
# More information on configuration properties: https://www.jhipster.tech/common-application-properties/
# ===================================================================

# ===================================================================
# Standard Spring Boot properties.
# Full reference is available at:
# http://docs.spring.io/spring-boot/docs/current/reference/html/common-application-properties.html
# ===================================================================

spring:
  application:
    name: gatewayService
  autoconfigure:
    exclude:
      - org.springframework.cloud.gateway.config.GatewayMetricsAutoConfiguration
  cloud:
    consul:
      discovery:
        enabled: false
        instanceId: ${spring.application.name}:${spring.application.instance-id:${random.value}}
      config:
        enabled: false
      enabled: false
  # Replace by 'prod, faker' to add the faker context and have sample data loaded in production
  liquibase:
    contexts: test
  jackson:
    serialization:
      write-durations-as-timestamps: false
  mail:
    host: localhost
  main:
    allow-bean-definition-overriding: true
  messages:
    basename: i18n/messages
  security:
    user:
      name: test
      password: test
      roles:
        - USER
    oauth2:
      resourceserver:
        jwt:
          authority-prefix: ''
          authorities-claim-name: auth
  task:
    execution:
      thread-name-prefix: gateway-service-task-
      pool:
        core-size: 1
        max-size: 50
        queue-capacity: 10000
    scheduling:
      thread-name-prefix: gateway-service-scheduling-
      pool:
        size: 20
  thymeleaf:
    mode: HTML

server:
  port: 10344
  address: localhost

# ===================================================================
# JHipster specific properties
#
# Full reference is available at: https://www.jhipster.tech/common-application-properties/
# ===================================================================
jhipster:
  clientApp:
    name: 'gatewayServiceApp'
  mail:
    from: <EMAIL>
    base-url: http://127.0.0.1:8080
  logging:
    # To test json console appender
    use-json-format: false
    logstash:
      enabled: false
      host: localhost
      port: 5000
      ring-buffer-size: 512
  security:
    authentication:
      jwt:
        # This token must be encoded using Base64 (you can type `echo 'secret-key'|base64` on your command line)
        base64-secret: MWQyZThlMWEwYjcyYTczYWM1ZWU3ZjJmM2YyMzViNzRlNDkzZjJhNDRlOTg2NzhiZjE4NTEzMDk0M2EwY2FiNjI4NzdiMGE0YmIwMjc1MjRjZDk1MzhiYjJiNTkxOWQzNjBlM2NlMDZiZDliNDg3OGQzMjlkNDE5ZWM3OTIxNTg=
        # Token is valid 24 hours
        token-validity-in-seconds: 86400
        token-validity-in-seconds-for-remember-me: 86400

# ===================================================================
# Application specific properties
# Add your own application properties here, see the ApplicationProperties class
# to have type-safe configuration, like in the JHipsterProperties above
#
# More documentation is available at:
# https://www.jhipster.tech/common-application-properties/
# ===================================================================

# application:
management:
  health:
    mail:
      enabled: false
