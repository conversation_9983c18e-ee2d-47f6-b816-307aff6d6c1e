# 🚀 WhiskerGuard API Gateway Service

<div align="center">

![Java](https://img.shields.io/badge/Java-17-orange.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.4.4-brightgreen.svg)
![Spring Cloud Gateway](https://img.shields.io/badge/Spring%20Cloud%20Gateway-4.2.1-blue.svg)
![License](https://img.shields.io/badge/License-MIT-yellow.svg)
![Build Status](https://img.shields.io/badge/Build-Passing-brightgreen.svg)

**企业级微服务网关 | 高性能 | 高可用 | 生产就绪**

</div>

## 📋 项目简介

WhiskerGuard API Gateway Service 是基于 **JHipster 8.10.0** 和 **Spring Cloud Gateway** 构建的企业级微服务网关，作为 WhiskerGuard 生态系统的统一入口。该服务提供了完整的网关解决方案，包括动态路由、安全认证、智能限流、熔断降级、监控告警等核心功能，确保系统的高性能、高可用和安全性。

### ✨ 核心特性

- 🔄 **动态路由** - 基于Consul的服务发现和动态路由配置
- 🛡️ **安全认证** - JWT认证、OAuth2集成、多层安全防护
- ⚡ **智能限流** - Redis+本地双重限流、多算法支持、自动降级
- 🔧 **熔断降级** - Resilience4j集成、自动故障恢复
- 📊 **全面监控** - 实时指标、健康检查、分布式追踪
- 🚀 **高性能** - 响应式编程、连接池优化、缓存加速

## 🛠️ 技术栈

### 核心框架
- **Java 17** - 现代Java特性支持
- **Spring Boot 3.4.4** - 企业级应用框架
- **Spring Cloud Gateway** - 响应式网关框架
- **Spring WebFlux** - 响应式Web框架
- **JHipster 8.10.0** - 现代化开发平台

### 安全认证
- **Spring Security** - 安全框架
- **OAuth2 Resource Server** - OAuth2资源服务器
- **JWT** - JSON Web Token认证

### 服务治理
- **Consul** - 服务发现和配置中心
- **Resilience4j** - 熔断器和重试机制
- **Reactive Feign** - 声明式HTTP客户端

### 数据存储
- **Redis** - 限流、缓存和会话存储
- **MySQL** - 关系型数据库

### 监控运维
- **Micrometer** - 应用指标收集
- **Spring Boot Actuator** - 应用监控端点
- **Zipkin** - 分布式链路追踪
- **Bucket4j** - 本地限流降级

## 📁 项目结构

```
whiskerguard-api-gateway-service/
├── src/main/java/com/whiskerguard/gateway/
│   ├── config/                    # 配置类
│   │   ├── RateLimiterConfiguration.java      # 限流器配置
│   │   ├── RateLimiterProperties.java         # 限流属性配置
│   │   ├── RateLimiterConfigurationManager.java # 配置管理器
│   │   ├── SecurityConfiguration.java         # 安全配置
│   │   ├── GatewayConfiguration.java         # 网关配置
│   │   └── FeignConfiguration.java           # Feign客户端配置
│   ├── filter/                    # 网关过滤器
│   │   ├── GlobalRateLimiterGatewayFilterFactory.java # 全局限流过滤器
│   │   ├── TenantRateLimiterGatewayFilterFactory.java # 租户限流过滤器
│   │   ├── AuthenticationGatewayFilterFactory.java   # 认证过滤器
│   │   └── LoggingGatewayFilterFactory.java          # 日志过滤器
│   ├── monitor/                   # 监控组件
│   │   ├── RateLimiterHealthIndicator.java    # 限流器健康检查
│   │   └── GatewayMetricsCollector.java       # 网关指标收集器
│   ├── security/                  # 安全组件
│   │   ├── jwt/                   # JWT相关
│   │   └── oauth2/                # OAuth2相关
│   ├── service/                   # 业务服务
│   │   └── client/                # Feign客户端
│   └── web/                       # Web层
│       └── rest/                  # REST控制器
├── src/main/resources/
│   ├── config/
│   │   ├── application.yml        # 主配置文件
│   │   ├── application-dev.yml    # 开发环境配置
│   │   └── application-prod.yml   # 生产环境配置
│   └── docker/                    # Docker配置文件
├── src/test/                      # 测试代码
├── docs/                          # 项目文档
│   ├── RATE_LIMITER_IMPROVEMENTS.md      # 限流器改进文档
│   ├── BEAN_CONFLICTS_RESOLUTION.md      # Bean冲突解决方案
│   └── feign-integration.md              # Feign集成指南
├── pom.xml                        # Maven配置
└── README.md                      # 项目说明
```

## 🎯 核心功能

### 🚀 已实现功能

#### 1. 🛡️ 企业级限流系统
- **多算法支持**: 令牌桶、滑动窗口、固定窗口算法
- **双重保障**: Redis主限流 + Bucket4j本地降级
- **智能重试**: 指数退避算法，防止雪崩效应
- **动态配置**: 支持运行时热重载，无需重启
- **全面监控**: 实时指标收集、健康检查、性能分析
- **多粒度限流**: 全局、IP、用户、租户级别限流

#### 2. 🔐 安全认证体系
- **JWT认证**: 完整的Token验证和管理
- **OAuth2集成**: 资源服务器配置
- **多层防护**: IP黑白名单、CIDR支持
- **会话管理**: Token活跃度检查、黑名单机制
- **安全头**: 完整的HTTP安全头配置

#### 3. 🔄 动态路由系统
- **服务发现**: Consul集成，自动服务注册发现
- **负载均衡**: 多种负载均衡策略
- **健康检查**: 实时服务健康状态监控
- **路由规则**: 灵活的路由配置和管理

#### 4. 📊 监控运维体系
- **实时监控**: Micrometer指标收集
- **健康检查**: 多维度健康状态检查
- **链路追踪**: Zipkin分布式追踪
- **日志管理**: 结构化日志，支持ELK集成
- **告警机制**: 基于指标的智能告警

#### 5. 🔧 高可用保障
- **熔断降级**: Resilience4j集成
- **故障恢复**: 自动故障检测和恢复
- **优雅降级**: 多级降级策略
- **容错机制**: 失败开放策略

#### 6. 🚀 性能优化
- **响应式编程**: 基于WebFlux的非阻塞处理
- **连接池优化**: Redis连接池配置
- **缓存策略**: 多级缓存机制
- **内存管理**: 智能内存清理，防止泄漏

### 🔄 持续改进功能

#### 1. 📈 高级监控
- **业务指标**: 自定义业务监控指标
- **性能分析**: 深度性能分析和优化建议
- **容量规划**: 基于历史数据的容量预测

#### 2. 🔒 安全增强
- **API安全**: API密钥管理和验证
- **数据加密**: 敏感数据传输加密
- **审计日志**: 完整的操作审计追踪

#### 3. 📚 API管理
- **文档聚合**: OpenAPI文档自动聚合
- **版本管理**: API版本控制和兼容性管理
- **测试工具**: 集成API测试工具

## 🚀 快速开始

### 📋 环境要求

| 组件 | 版本要求 | 说明 |
|------|----------|------|
| **JDK** | 17+ | 支持现代Java特性 |
| **Maven** | 3.6.0+ | 项目构建工具 |
| **Docker** | 20.0+ | 容器化部署 |
| **Docker Compose** | 2.0+ | 多容器编排 |
| **Consul** | 1.15+ | 服务发现和配置中心 |
| **Redis** | 6.0+ | 缓存和限流存储 |

### 🛠️ 开发环境快速启动

#### 1. 克隆项目
```bash
git clone https://github.com/yanhaishui/whiskerguard-api-gateway-service.git
cd whiskerguard-api-gateway-service
```

#### 2. 启动基础设施服务
```bash
# 启动Consul (服务发现)
docker compose -f src/main/docker/consul.yml up -d

# 启动Redis (限流和缓存)
docker compose -f src/main/docker/redis.yml up -d

# 验证服务状态
docker ps
```

#### 3. 配置环境变量 (可选)
```bash
export SPRING_PROFILES_ACTIVE=dev
export CONSUL_HOST=localhost
export REDIS_HOST=localhost
```

#### 4. 启动网关服务
```bash
# 开发模式启动
./mvnw spring-boot:run

# 或者使用IDE启动主类
# com.whiskerguard.gateway.GatewayApp
```

#### 5. 验证服务启动
```bash
# 健康检查
curl http://localhost:8080/management/health

# 查看服务注册状态
curl http://localhost:8500/v1/agent/services
```

### 🏭 生产环境部署

#### 1. 构建生产包
```bash
# 清理并构建
./mvnw clean package -Pprod -DskipTests

# 构建Docker镜像
./mvnw -Pprod verify jib:dockerBuild
```

#### 2. 使用Docker部署
```bash
# 构建镜像
docker build -t whiskerguard/gateway:latest .

# 运行容器
docker run -d \
  --name whiskerguard-gateway \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e CONSUL_HOST=consul-server \
  -e REDIS_HOST=redis-server \
  whiskerguard/gateway:latest
```

#### 3. 使用Docker Compose部署
```bash
# 完整环境部署
docker compose -f src/main/docker/app.yml up -d

# 查看服务状态
docker compose -f src/main/docker/app.yml ps
```

## ⚙️ 配置说明

### 📝 主要配置文件

| 配置文件 | 用途 | 说明 |
|----------|------|------|
| `application.yml` | 主配置文件 | 基础配置和默认值 |
| `application-dev.yml` | 开发环境 | 开发环境特定配置 |
| `application-prod.yml` | 生产环境 | 生产环境优化配置 |

### 🔧 核心配置项

#### 1. 服务基础配置
```yaml
server:
  port: 8080
spring:
  application:
    name: gateway
  profiles:
    active: dev
```

#### 2. 服务发现配置
```yaml
spring:
  cloud:
    consul:
      host: localhost
      port: 8500
      discovery:
        prefer-ip-address: true
        health-check-path: /management/health
```

#### 3. 限流器配置
```yaml
rate-limiter:
  rules:
    global:
      replenishRate: 100          # 每秒补充令牌数
      burstCapacity: 100          # 桶最大容量
      timeWindow: 1               # 时间窗口(秒)
      algorithm: TOKEN_BUCKET     # 限流算法
      enableRetry: true           # 启用重试
      enableLocalFallback: true   # 启用本地降级
```

#### 4. 安全认证配置
```yaml
spring:
  security:
    oauth2:
      resourceserver:
        jwt:
          issuer-uri: http://localhost:9080/realms/jhipster
```

#### 5. Redis配置
```yaml
spring:
  data:
    redis:
      host: localhost
      port: 6379
      timeout: 2000ms
      lettuce:
        pool:
          max-active: 8
          max-idle: 8
```

### 🔀 路由配置示例

```yaml
spring:
  cloud:
    gateway:
      routes:
        - id: auth-service
          uri: lb://auth-service
          predicates:
            - Path=/api/auth/**
          filters:
            - name: GlobalRateLimiter
              args:
                replenishRate: 50
                burstCapacity: 100
```

## 👨‍💻 开发指南

### 🔌 Feign 客户端集成

项目集成了 **Reactive Feign** 客户端，提供声明式HTTP服务调用。详细指南：[Feign集成文档](docs/feign-integration.md)

#### 核心特性
- ✅ **声明式客户端** - 接口定义即可使用
- ✅ **负载均衡** - 自动服务发现和负载均衡
- ✅ **熔断器集成** - Resilience4j熔断保护
- ✅ **重试机制** - 智能重试策略
- ✅ **拦截器支持** - 请求/响应拦截处理

#### 使用示例
```java
@ReactiveFeignClient(name = "auth-service", configuration = FeignConfiguration.class)
public interface AuthServiceClient {

    @PostMapping("/api/auth/validate")
    Mono<TokenValidationResponse> validateToken(
        @RequestHeader("Authorization") String token
    );

    @GetMapping("/api/users/{id}")
    Mono<UserInfo> getUserInfo(@PathVariable("id") Long userId);
}
```

### 🎨 自定义过滤器开发

#### 创建自定义过滤器
```java
@Component
public class CustomGatewayFilterFactory extends AbstractGatewayFilterFactory<CustomGatewayFilterFactory.Config> {

    @Override
    public GatewayFilter apply(Config config) {
        return (exchange, chain) -> {
            // 前置处理
            ServerHttpRequest request = exchange.getRequest();

            return chain.filter(exchange).then(Mono.fromRunnable(() -> {
                // 后置处理
                ServerHttpResponse response = exchange.getResponse();
            }));
        };
    }

    public static class Config {
        // 配置属性
    }
}
```

### 📊 限流器使用指南

#### 配置限流规则
```yaml
rate-limiter:
  rules:
    api-service:
      replenishRate: 100
      burstCapacity: 200
      algorithm: TOKEN_BUCKET
      enableRetry: true
      pathPattern: "/api/**"
```

#### 动态配置更新
```java
@Autowired
private RateLimiterConfigurationManager configManager;

// 获取当前配置
Map<String, Rule> rules = configManager.getActiveRules();

// 检查规则是否存在
boolean hasRule = configManager.hasRule("api-service");
```

### 🧪 测试指南

#### 单元测试
```bash
# 运行所有测试
./mvnw test

# 运行特定测试类
./mvnw test -Dtest=GlobalRateLimiterGatewayFilterFactoryTest

# 生成测试报告
./mvnw test jacoco:report
```

#### 集成测试
```bash
# 启动测试环境
docker compose -f src/main/docker/consul.yml up -d
docker compose -f src/main/docker/redis.yml up -d

# 运行集成测试
./mvnw verify -Pintegration-test
```

### 🐳 Docker 开发

#### 本地开发镜像
```bash
# 构建开发镜像
./mvnw spring-boot:build-image

# 运行开发容器
docker run -p 8080:8080 whiskerguard/gateway:latest
```

#### 多阶段构建
```dockerfile
FROM openjdk:17-jdk-slim as builder
WORKDIR /app
COPY . .
RUN ./mvnw clean package -DskipTests

FROM openjdk:17-jre-slim
COPY --from=builder /app/target/*.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

### 📏 代码规范

#### 代码风格
- 遵循 **Google Java Style Guide**
- 使用 **Checkstyle** 进行静态检查
- 使用 **SpotBugs** 进行bug检测

#### 提交规范
```bash
# 提交格式
git commit -m "feat(限流): 添加动态配置支持"
git commit -m "fix(安全): 修复JWT验证问题"
git commit -m "docs(README): 更新部署文档"
```

#### 代码检查
```bash
# 运行代码检查
./mvnw checkstyle:check
./mvnw spotbugs:check

# 格式化代码
./mvnw prettier:write
```

## 📊 监控和运维

### 🏥 健康检查

#### 主要端点
| 端点 | 功能 | 示例 |
|------|------|------|
| `/management/health` | 整体健康状态 | `curl http://localhost:8080/management/health` |
| `/management/health/consul` | Consul连接状态 | 服务发现健康检查 |
| `/management/health/redis` | Redis连接状态 | 缓存和限流健康检查 |
| `/management/health/rateLimiter` | 限流器状态 | 自定义限流器健康检查 |

#### 健康检查响应示例
```json
{
  "status": "UP",
  "components": {
    "consul": {"status": "UP"},
    "redis": {"status": "UP"},
    "rateLimiter": {
      "status": "UP",
      "details": {
        "redis": {"status": "UP", "responseTime": "2.5ms"},
        "rateLimiting": {"successRate": "99.8%"},
        "localFallback": {"activations": 0}
      }
    }
  }
}
```

### 📈 指标监控

#### 核心指标端点
- **`/management/metrics`** - 所有可用指标
- **`/management/prometheus`** - Prometheus格式指标

#### 关键业务指标
```bash
# 限流器指标
curl http://localhost:8080/management/metrics/rate_limiter_allowed_total
curl http://localhost:8080/management/metrics/rate_limiter_limited_total

# 网关性能指标
curl http://localhost:8080/management/metrics/gateway_requests_total
curl http://localhost:8080/management/metrics/http_server_requests

# JVM指标
curl http://localhost:8080/management/metrics/jvm_memory_used
curl http://localhost:8080/management/metrics/jvm_gc_pause
```

### 📋 日志管理

#### 日志配置
```yaml
logging:
  level:
    com.whiskerguard.gateway: DEBUG
    org.springframework.cloud.gateway: INFO
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{ISO8601} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/gateway.log
```

#### 结构化日志示例
```json
{
  "timestamp": "2024-01-15T10:30:00.123Z",
  "level": "INFO",
  "logger": "com.whiskerguard.gateway.filter.GlobalRateLimiterGatewayFilterFactory",
  "message": "限流检查通过",
  "mdc": {
    "traceId": "abc123",
    "spanId": "def456",
    "userId": "user123",
    "requestId": "req789"
  }
}
```

### 🔍 链路追踪

#### Zipkin集成
```yaml
spring:
  zipkin:
    base-url: http://localhost:9411
  sleuth:
    sampler:
      probability: 1.0  # 开发环境100%采样
```

## ❓ 常见问题

### 🔧 服务发现问题

**问题**: 服务无法注册到Consul
```bash
# 检查Consul状态
curl http://localhost:8500/v1/agent/services

# 检查网络连接
telnet localhost 8500

# 查看服务日志
docker logs consul-container
```

**解决方案**:
1. 确保Consul服务正常运行
2. 检查网络配置和防火墙
3. 验证服务配置中的Consul地址

### 🛡️ 认证问题

**问题**: JWT验证失败
```bash
# 检查Token格式
curl -H "Authorization: Bearer <token>" http://localhost:8080/api/test

# 验证JWT配置
curl http://localhost:8080/management/configprops
```

**解决方案**:
1. 验证JWT签名密钥配置
2. 检查Token过期时间
3. 确认issuer-uri配置正确

### ⚡ 限流问题

**问题**: 限流器不生效
```bash
# 检查限流器健康状态
curl http://localhost:8080/management/health/rateLimiter

# 查看限流指标
curl http://localhost:8080/management/metrics/rate_limiter_allowed_total
```

**解决方案**:
1. 检查Redis连接状态
2. 验证限流配置
3. 查看限流器日志

### 🔄 路由问题

**问题**: 请求路由失败
```bash
# 检查路由配置
curl http://localhost:8080/management/gateway/routes

# 查看服务实例
curl http://localhost:8500/v1/health/service/target-service
```

## 🤝 贡献指南

### 📝 贡献流程
1. **Fork项目** - 点击右上角Fork按钮
2. **创建分支** - `git checkout -b feature/amazing-feature`
3. **提交代码** - `git commit -m 'feat: 添加新功能'`
4. **推送分支** - `git push origin feature/amazing-feature`
5. **创建PR** - 在GitHub上创建Pull Request

### 🎯 贡献指南
- 遵循现有代码风格和规范
- 添加适当的测试用例
- 更新相关文档
- 确保所有测试通过

### 📋 Issue模板
- **Bug报告**: 详细描述问题和复现步骤
- **功能请求**: 说明需求和使用场景
- **文档改进**: 指出文档不足之处

## 📄 许可证

本项目采用 [MIT License](LICENSE) 开源协议。

## 📞 联系方式

- **项目维护者**: WhiskerGuard Team
- **邮箱**: [<EMAIL>](mailto:<EMAIL>)
- **GitHub**: [https://github.com/yanhaishui/whiskerguard-api-gateway-service](https://github.com/yanhaishui/whiskerguard-api-gateway-service)
- **文档**: [项目Wiki](https://github.com/yanhaishui/whiskerguard-api-gateway-service/wiki)

---

<div align="center">

**⭐ 如果这个项目对您有帮助，请给我们一个Star！⭐**

Made with ❤️ by WhiskerGuard Team

</div>
