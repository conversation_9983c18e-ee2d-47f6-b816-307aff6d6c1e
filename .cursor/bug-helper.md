---

## ✅ 2. `.cursor/bug-helper.md`

```markdown
# 🐞 网关服务问题排查模板（Cursor IDE）

## 📌 当前服务：

- whiskerguard-api-gateway-service

## 🐛 问题描述：

简要说明遇到的问题：

- 是否是 JWT 校验失败？
- 是否是转发异常（404 / 502）？
- 是否是限流、熔断、重试无效？
- 是否是上下文未注入成功？

## 🧪 复现步骤：

1. 调用接口（如：`/api/auth/me`）
2. JWT 携带方式（Header / URL）
3. 响应错误码或异常信息
4. 期望行为与实际行为对比

## 🔎 关键代码（粘贴相关文件）：

- Security 配置类
- GatewayFilter 或 Route 配置
- application.yml 的相关配置段

## 🧾 日志信息（建议贴 ERROR 日志）：

[ERROR] JwtAuthenticationConverter - Missing authority field in claims
[WARN] GatewayFilterChain - Rejected by IP Blacklist: ********

## 🧠 环境信息：

- Profile: `dev` / `jwt` / `gray`
- JWT 模式：本地解码 / introspect
- 限流配置来源：Redis / 内存
- 是否连接 Auth Service：是 / 否

## 🎯 你的目标：

- [ ] 分析问题根因
- [ ] 给出修复建议
- [ ] 检查路由或过滤器配置
- [ ] 提供限流或熔断策略优化建议
```
