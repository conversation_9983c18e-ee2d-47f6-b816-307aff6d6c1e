# whiskerguard-api-gateway-service 上下文说明（Cursor IDE 专用）

## 📌 项目定位

本服务是猫伯伯合规平台的「统一 API 网关服务」，基于 Spring Cloud Gateway + JHipster 架构，承担路由转发、安全过滤、限流熔断、文档聚合、上下文注入等职责。

## 🧠 当前开发身份

你是 AI 编程助手，我是系统架构师。回答需精炼、企业级，不解释基础概念。请聚焦**JWT 安全链路、过滤器开发、性能优化、网关中台能力建设**。

---

## ✅ 实现能力（已完成）

| 模块                     | 状态 | 简要说明                              |
| ------------------------ | ---- | ------------------------------------- |
| Consul 服务发现          | ✅   | 路由自动加载，支持动态刷新            |
| JWT 校验（资源服务器）   | ✅   | 支持本地解析与 introspect 检查        |
| GatewayFilter 上下文注入 | ✅   | 注入 X-User-_ / X-Tenant-_            |
| IP 黑白名单控制          | ✅   | 基于 CIDR 检查，支持敏感路径限制      |
| 限流（Redis）            | ✅   | IP/用户/租户限流，Bucket4j + Redis    |
| 重试机制                 | ✅   | 指数退避 + 抖动 + 动态服务配置        |
| 熔断机制（Resilience4j） | ✅   | 支持服务级回退与降级页面              |
| 统一异常处理             | ✅   | ProblemDetails 格式 JSON 响应         |
| 动态路由配置             | ✅   | 支持 Consul KV + Redis 双配置         |
| Swagger 聚合             | ✅   | 聚合 /v3/api-docs，暴露 swagger-ui    |
| Prometheus 指标          | ✅   | 已接入 Micrometer，支持 auth 请求指标 |
| 健康检查增强             | ✅   | 包含 Auth Service 探活与状态同步      |
| CORS + 安全头            | ✅   | 全局配置，支持跨源访问控制            |

---

## 🛠️ 核心依赖技术栈

- Spring Cloud Gateway + Security + Resilience4j
- Redis + Bucket4j（限流）
- SpringDoc + Swagger 聚合
- Sleuth + Zipkin（链路追踪）
- Prometheus + Micrometer（指标采集）
- Consul 服务发现与配置

---

## 🔗 微服务交互

- 接入服务：`auth-service`（Token 校验、刷新、权限注入）
- 所有微服务统一通过 `/api/**` 路由转发
- Token 校验逻辑：
  - 默认使用本地解码（HS512）
  - 配置支持 introspect 转发至 auth-service
  - 支持黑名单校验、Token 活跃度检查

---

## 📣 特别说明

- 所有请求头注入项必须符合格式：
  - `X-User-Id`, `X-Tenant-Id`, `X-User-Authorities`, `X-Request-ID`
- JWT Claims 中必须包含 `tenant_id`、`sub`、`authorities`
- 默认异常响应格式：
  ```json
  {
    "type": "https://www.jhipster.tech/problem/problem-with-message",
    "title": "Unauthorized",
    "status": 401,
    "message": "error.http.401",
    "path": "/api/xxx"
  }
  ```
