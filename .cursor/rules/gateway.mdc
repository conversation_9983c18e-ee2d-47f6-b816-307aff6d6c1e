---
description:
globs:
alwaysApply: false
---
# JHipster 网关服务规范

## 路由配置
- 使用 Spring Cloud Gateway 进行路由配置
- 路由规则必须定义在 application.yml 中
- 每个微服务路由必须配置超时时间
- 必须配置断路器（Circuit Breaker）策略

## 服务发现与配置
- 使用 Consul 进行服务注册和发现
- 使用 Consul 进行配置管理
- 配置服务健康检查
- 实现服务自动注册

## 安全配置
- 实现 OAuth2 认证和授权
- 配置 CORS 策略
- 实现请求限流（Rate Limiting）
- 配置 SSL/TLS 证书

## 监控和日志
- 集成 Prometheus 监控
- 配置 ELK 日志收集
- 实现分布式追踪（使用 Zipkin/Jaeger）
- 记录网关访问日志

## 性能优化
- 配置响应缓存
- 实现请求压缩
- 配置连接池
- 实现负载均衡策略
