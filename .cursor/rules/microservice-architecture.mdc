---
description:
globs:
alwaysApply: false
---
# 微服务架构规范

## 服务注册与发现
- 使用 Consul 进行服务注册
- 实现服务自动发现
- 配置服务健康检查
- 实现服务优雅下线

## 服务通信
- 使用 OpenFeign 进行服务间调用
- 实现服务降级和熔断
- 使用消息队列进行异步通信
- 实现服务重试机制

## 配置管理
- 使用 Consul 进行配置管理
- 实现配置自动刷新
- 配置环境隔离
- 实现配置版本控制

## 数据一致性
- 使用分布式事务（如 Seata）
- 实现最终一致性模式
- 使用事件溯源模式
- 实现补偿机制

## 服务治理
- 实现服务注册与发现
- 配置负载均衡策略
- 实现服务限流和熔断
- 配置服务降级策略

## 部署规范
- 使用 Docker 容器化部署
- 配置 Kubernetes 资源
- 实现蓝绿部署
- 配置自动扩缩容
