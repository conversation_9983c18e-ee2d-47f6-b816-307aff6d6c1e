---
description:
globs:
alwaysApply: false
---
# Java 代码风格规范

## 代码风格
- 使用 4 个空格进行缩进
- 最大行长度限制为 120 字符
- 类名使用 PascalCase
- 方法名和变量名使用 camelCase
- 常量使用全大写下划线分隔
- 所有公共类和方法必须有 JavaDoc 注释

## 包结构
- 基础包名必须是 com.whiskerguard
- 遵循 JHipster 分层架构：
  - web.rest：REST 控制器
  - service：业务逻辑层
  - repository：数据访问层
  - domain：领域模型
  - config：配置类
  - security：安全相关
  - client：微服务客户端
  - gateway：网关相关
- 网关服务特有包结构：
  - filter：网关过滤器
  - monitoring：监控相关
  - aop：面向切面编程
  - management：管理接口
  - health：健康检查
- 测试类必须与被测试类在同一包下

## 代码质量
- 方法长度不应超过 50 行
- 类长度不应超过 500 行
- 圈复杂度不应超过 10
- 测试覆盖率要求达到 80%
