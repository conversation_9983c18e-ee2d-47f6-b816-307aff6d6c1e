# 💡 网关服务开发高效 Prompt 模板（适配 Cursor IDE）

## ✅ 1. JWT / 安全配置相关

```text
【模块】SecurityJwtConfiguration
【背景】当前使用 spring.security.oauth2.resourceserver.jwt 本地解码
【目标】请帮我补充 JwtAuthenticationConverter，使其正确提取 authorities 字段
【要求】使用 authorities 而非 scope；支持默认前缀移除（如 ROLE_）


## 2.自定义过滤器开发

【模块】UserInfoGatewayFilter
【背景】需要将 user_id、tenant_id、authorities 注入下游 Header
【目标】请生成过滤器代码，支持未认证时默认值为空，记录日志
【要求】Header 示例：X-User-Id, X-Tenant-Id, X-User-Authorities

## 3.限流策略生成

【场景】我需要实现租户级别的 Redis 限流
【目标】请生成 GatewayFilter + RedisRateLimiter 配置
【要求】
- 路径 /api/tenant/** 限制 200 QPS，突发 300
- 响应添加 X-RateLimit-* 相关头
- 限流失败返回 429 JSON 格式错误提示


## 4. 异常排查

【问题】访问 /api/xxx 返回 403，但 JWT 合法
【背景】我已启用 introspect + 黑名单检查
【日志】（粘贴错误日志）
【目标】请分析是否为 Spring Security 配置问题或 Filter 顺序问题，并建议修复方式

## 5. 动态路由配置调试

【问题】我更新了 Consul KV 的路由信息，但 /api/user/** 没有生效
【配置】Consul KV 开启 watch，路由配置已变更
【目标】请检查是否需要手动触发刷新、注册配置类或添加日志，生成修复建议


## 6. Swagger 聚合文档配置

【目标】我想在 Gateway 聚合多个微服务的 /v3/api-docs 文档
【背景】下游服务均支持 SpringDoc
【要求】请生成使用 WebClient 聚合多个 OpenAPI 文档的聚合 Controller，实现 /v3/api-docs-aggregate 端点


## 7.
```
