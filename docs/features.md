# whiskerguard-api-gateway-service 功能清单

以下是基于 JHipster 和 Consul 服务发现构建的 `gateway-service` 所需功能列表，供 Cursor Agent 读取并自动生成对应代码：

---

## 1. 路由配置（Consul 服务发现）

- 从 Consul 注册中心动态获取后端微服务实例列表
- 基于 URL 前缀（`/api/**`、`/api/**`）或 Header 执行路由转发
- 路由元数据存储在 Consul KV 和 Spring Cloud Config 中，支持实时刷新

## 2. JWT 验证过滤器

- 使用 JHipster 的 `spring.security.oauth2.resourceserver.jwt` 配置进行本地验签
- 拦截所有 `/api/**` 路径，校验签名与过期
- 可选集成 `POST /auth/introspect` 接口，完成 Token 活跃度检查

## 3. 上下文注入

- 从 JWT Claims 中提取 `user_id`、`tenant_id`、`roles`
- 通过自定义 `GatewayFilter` 将这些值写入下游请求 Header（如 `X-User-Id`、`X-Tenant-Id`）

## 4. 限流 & 重试

- 全局及单用户/单 IP 限流（基于 `RedisRateLimiter`）
- 配置重试策略（`Retry` Filter）：重试次数、重试条件、回退间隔

## 5. 熔断 & 回退

- 集成 Resilience4j 和 Spring Cloud CircuitBreaker
- 针对关键路由设置熔断阈值与半开恢复策略
- 提供全局或按路由的自定义回退方法

## 6. 分布式追踪 & 日志

- 集成 Spring Cloud Sleuth + Zipkin/OpenTelemetry
- 为每次请求生成 Trace ID 并传递给下游服务，日志中记录请求耗时与状态码

## 7. 全局异常处理

- 统一捕获网关层异常，返回标准化 JSON 错误响应（400/401/403/502 等）
- 保持与 JHipster Problem Details 格式或自定义包装一致

## 8. IP 黑白名单

- 基于 CIDR 定义访问白名单和黑名单
- 针对敏感路径（如 `/management/**`）进行额外访问控制

## 9. 跨域 & 安全头

- 全局 CORS 配置：允许的域名、方法和 Header
- 自动添加安全 HTTP Header（HSTS、X-Content-Type-Options、Content-Security-Policy 等）

## 10. 灰度发布 & 动态路由

- 路由配置可从 Consul/KV 动态拉取并实时刷新
- 按 Header、User ID 范围或百分比比例执行灰度流量分发

## 11. Swagger/OpenAPI 聚合

- 聚合下游各微服务的 OpenAPI 文档，提供统一的 `/v3/api-docs` 聚合端点
- 暴露统一的 Swagger-UI 界面，方便前端和测试工具使用

## 12. 缓存 & 响应加速

- 对幂等的 GET 请求配置 `Cache-Control` 缓存策略
- 使用 Spring Cache + Redis 实现短时响应缓存

## 13. 健康检查 & 自我保护

- 定期调用下游服务的 `/actuator/health` 端点，动态更新可用实例列表
- 下游实例故障率过高时自动下线，避免级联故障

## 14. 监控指标

- 暴露 Gateway 自身的 Prometheus 指标：请求总数、成功/失败数、延迟分布等
- 与 `monitoring-service` 集成，推送关键告警信息

---
