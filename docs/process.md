# WhiskerGuard 网关服务实施计划

## 当前状态分析

### 已实现的功能：

1. 基础路由配置（通过 Consul 服务发现）
2. JWT 验证（基础配置已完成）
3. 基础的安全头配置
4. 基础的监控指标（通过 Actuator）
5. 基础的跨域配置
6. 基础的分布式追踪（通过 Zipkin）
7. 基础的异常处理（通过 Problem Details）

### 待实现的功能：

1. **Auth Service 集成**：

   - 实现 OAuth2 资源服务器配置
   - 集成 Token Introspection 接口
   - 实现 Token 黑名单检查
   - 配置用户信息注入

2. **路由配置增强**：

   - 需要实现动态路由配置的 Consul KV 存储
   - 需要实现路由元数据的实时刷新机制

3. **JWT 验证增强**：

   - 需要实现 Token 活跃度检查接口
   - 需要完善 JWT Claims 的提取和验证
   - 需要支持多种授权模式（密码模式、刷新令牌、客户端凭证）

4. **上下文注入**：

   - 需要实现自定义 GatewayFilter
   - 需要实现用户信息注入机制
   - 需要注入租户信息（tenant_id）
   - 需要注入用户角色和权限信息

5. **限流与重试**：

   - 需要实现 Redis 限流器
   - 需要配置重试策略
   - 需要实现基于用户和租户的限流

6. **熔断与回退**：

   - 需要集成 Resilience4j
   - 需要实现自定义回退策略
   - 需要处理 Auth Service 不可用的情况

7. **IP 黑白名单**：

   - 需要实现 CIDR 检查机制
   - 需要实现敏感路径访问控制
   - 需要与 Auth Service 的审计日志集成

8. **灰度发布**：

   - 需要实现动态路由配置
   - 需要实现流量分发策略
   - 需要支持基于租户的灰度发布

9. **API 文档聚合**：

   - 需要实现 OpenAPI 文档聚合
   - 需要配置统一的 Swagger UI
   - 需要集成 Auth Service 的 API 文档

10. **缓存机制**：

    - 需要实现响应缓存策略
    - 需要集成 Redis 缓存
    - 需要缓存用户权限信息

11. **健康检查增强**：
    - 需要实现实例健康检查机制
    - 需要实现自我保护机制
    - 需要监控 Auth Service 的健康状态

## 实施计划

### 第一阶段：Auth Service 集成（预计 1 周）

1. **OAuth2 资源服务器配置**

   - 配置 JWT 验证
   - 实现 Token Introspection
   - 配置 Token 黑名单检查

2. **用户信息处理**

   - 实现用户信息提取
   - 配置租户信息注入
   - 实现角色和权限注入

3. **安全配置**
   - 配置安全路由规则
   - 实现认证失败处理
   - 配置审计日志

### 第二阶段：基础功能增强（预计 2 周）

1. **JWT 验证增强**

   - 实现 Token 活跃度检查接口
   - 完善 JWT Claims 提取和验证
   - 添加 Token 刷新机制

   **已实现代码**：

   - 新增文件：`src/main/java/com/whiskerguard/gateway/web/rest/TokenResource.java`
     - 实现了 Token 活跃度检查接口，提供 `/api/auth/introspect` 端点
     - 支持验证 Token 有效性并返回用户信息
     - 添加了 Token 刷新接口，提供 `/api/auth/refresh` 端点
     - 支持使用有效 Token 获取新的 Token
   - 新增文件：`src/test/java/com/whiskerguard/gateway/web/rest/TokenResourceTest.java`
     - 添加了 Token 活跃度检查接口的测试用例
     - 包含有效 Token 和无效 Token 的测试场景
     - 添加了 Token 刷新接口的测试用例
     - 包含有效 Token、无效 Token 和无 Token 的测试场景
   - 修改文件：`src/main/java/com/whiskerguard/gateway/config/SecurityJwtConfiguration.java`
     - 添加了 `JwtAuthenticationConverter` Bean 配置
     - 配置了 `JwtGrantedAuthoritiesConverter` 用于正确处理 JWT token 中的权限信息
     - 移除了重复的 `ROLE_` 前缀，确保权限名称正确显示
   - 修改文件：`src/main/java/com/whiskerguard/gateway/config/SecurityConfiguration.java`
     - 注入了 `JwtAuthenticationConverter` Bean
     - 配置了 OAuth2 资源服务器使用自定义的 `JwtAuthenticationConverter`
     - 使用 `ReactiveJwtAuthenticationConverterAdapter` 适配响应式环境
   - 修改文件：`src/main/java/com/whiskerguard/gateway/security/jwt/JWTTokenProvider.java`
     - 修改了 `createTestToken` 方法，使用 `authorities` 作为权限声明的 key
     - 确保生成的 token 包含正确的权限信息

   **测试结果**：

   - 成功实现了 token 生成和验证
   - 成功实现了 token 活跃度检查
   - 成功实现了权限信息的正确提取和显示
   - 成功处理了未提供 token 的情况
   - 成功实现了 token 刷新功能
   - 成功处理了 token 刷新的各种场景（有效 token、无效 token、无 token）

2. **上下文注入**

   - 实现自定义 GatewayFilter
   - 实现用户信息注入机制
   - 添加请求追踪信息

   **已实现代码**：

   - 新增文件：`src/main/java/com/whiskerguard/gateway/filter/UserInfoGatewayFilter.java`
     - 实现了自定义的 GatewayFilter
     - 支持注入用户信息到请求头
     - 添加请求追踪 ID
     - 记录用户访问日志
   - 新增文件：`src/main/java/com/whiskerguard/gateway/config/GatewayFilterConfiguration.java`
     - 配置了自定义路由规则
     - 注册了 UserInfoGatewayFilter
     - 配置了过滤器应用到所有 API 请求
   - 新增文件：`src/test/java/com/whiskerguard/gateway/filter/UserInfoGatewayFilterTest.java`
     - 添加了用户信息注入的测试用例
     - 包含认证和非认证场景的测试
     - 验证请求头注入的正确性

   **功能说明**：

   - 用户信息注入：
     - 注入用户名到 `X-User-Name` 请求头
     - 注入用户权限到 `X-User-Authorities` 请求头
     - 支持未认证请求的处理
   - 请求追踪：
     - 为每个请求生成唯一的 `X-Request-ID`
     - 记录用户访问日志，包含用户名、请求方法和 URI
   - 路由配置：
     - 将过滤器应用到所有 `/api/**` 路径
     - 支持服务发现和负载均衡

3. **IP 黑白名单**

   - 实现 CIDR 检查机制
   - 配置敏感路径访问控制
   - 添加 IP 访问日志

     **IP 黑白名单代码实现历史**

     - 新增文件：
     - `src/main/java/com/whiskerguard/gateway/filter/IpAccessControlFilter.java`

       - 实现了 IP 访问控制的核心逻辑
       - 支持 CIDR 格式的 IP 地址匹配
       - 实现了黑名单和白名单模式的访问控制
       - 支持敏感路径的访问控制
       - 包含详细的日志记录

     - `src/main/java/com/whiskerguard/gateway/config/IpAccessControlProperties.java`

       - 定义了 IP 访问控制的配置属性
       - 支持白名单模式开关
       - 支持白名单、黑名单和敏感路径的配置
       - 包含配置初始化日志

     - `src/main/java/com/whiskerguard/gateway/config/IpAccessControlConfiguration.java`

       - 配置了 IP 访问控制的路由规则
       - 设置了测试端点用于验证功能
       - 配置了路由优先级

     - 修改文件：
     - `src/main/resources/config/application.yml`

       - 添加了 IP 访问控制的配置部分
       - 配置了默认的白名单（127.0.0.1/32, ***********/24）
       - 配置了默认的黑名单（10.0.0.0/8）
       - 配置了敏感路径（/api/admin/._, /api/internal/._, /api/management/.\*）
       - 在 Spring Cloud Gateway 路由配置中添加了 IP 访问控制过滤器

     - 功能特点：
     - 支持 CIDR 格式的 IP 地址匹配
     - 支持白名单和黑名单两种模式
     - 支持敏感路径的访问控制
     - 详细的访问日志记录
     - 灵活的配置管理
     - 与 Spring Cloud Gateway 完美集成

     - 配置示例：

     ```yaml
     gateway:
       ip-access-control:
         whitelist-mode: false
         whitelist:
           - '127.0.0.1/32'
           - '***********/24'
         blacklist:
           - '10.0.0.0/8'
         sensitive-paths:
           - '/api/admin/.*'
           - '/api/internal/.*'
           - '/api/management/.*'
     ```

### 第三阶段：性能优化（预计 2 周）

1. **限流机制**

   - 集成 Redis 限流器
   - 实现全局限流策略
   - 实现用户级限流
   - 实现租户级限流

   **限流机制代码实现历史**

   - 新增文件：
     - `src/main/java/com/whiskerguard/gateway/config/RateLimiterConfiguration.java`
       - 配置全局限流器：100 请求/秒，突发容量 100
       - 配置 IP 限流器：50 请求/秒，突发容量 50
       - 配置用户限流器：20 请求/秒，突发容量 20
       - 配置租户限流器：200 请求/秒，突发容量 200
     - `src/main/java/com/whiskerguard/gateway/filter/RateLimiterFilter.java`
       - 实现基于 Redis 的限流过滤器
       - 添加限流响应头：X-RateLimit-Remaining, X-RateLimit-Limit, X-RateLimit-Reset
   - 修改文件：
     - `src/main/resources/config/application.yml`
       - 配置全局限流路由：`/api/**`，基于 IP 地址限流
       - 配置租户限流路由：`/api/tenant/**`，基于租户 ID 限流
       - 配置用户限流路由：`/api/user/**`，基于用户 ID 限流

   **限流机制代码更新历史**

   - 添加 Bucket4j 依赖，实现本地限流降级策略
   - 优化 Redis 限流器配置，添加连接池和健康检查
   - 实现动态限流规则配置，支持运行时调整限流参数
   - 添加限流监控和统计功能，集成 Prometheus 指标收集
   - 修复限流器响应头设置，优化限流事件日志记录
   - 修正路由配置格式，优化过滤器链配置
   - 实现多级限流策略（全局、用户、租户），支持优先级管理
   - 添加限流规则的版本控制和审计日志
   - 优化限流降级策略，实现 Redis 不可用时的本地限流
   - 完善限流器监控指标，添加限流成功/失败计数器

2. **重试策略**

   - 配置重试条件
   - 实现重试间隔策略
   - 添加重试日志
   - 配置 Auth Service 调用重试

   **重试策略代码实现历史**

   - 新增文件：
     - `src/main/java/com/whiskerguard/gateway/config/RetryProperties.java`
       - 实现了重试配置属性类，支持动态配置更新
       - 支持服务级别的重试策略配置
       - 可配置重试参数（最大尝试次数、初始间隔、最大间隔等）
       - 支持配置重试条件和重试方法
     - `src/main/java/com/whiskerguard/gateway/config/RetryConfiguration.java`
       - 配置了重试机制，提供指数退避重试策略
       - 实现了服务级别的重试配置
       - 集成了断路器配置
       - 支持自定义重试条件
     - `src/main/java/com/whiskerguard/gateway/monitoring/RetryMetrics.java`
       - 实现了重试监控类，收集和报告重试指标
       - 记录重试次数、失败次数和重试速率
       - 集成 Prometheus 指标收集
       - 提供详细的重试统计日志
   - 修改文件：
     - `src/main/resources/config/application.yml`
       - 添加了重试配置部分
       - 配置了默认重试策略（3次重试，1-10秒间隔）
       - 配置了服务级别的重试策略：
         - 认证服务：3次重试，1-5秒间隔
         - 用户服务：2次重试，0.5-3秒间隔
       - 在路由配置中添加了重试过滤器
   - 功能特点：
     - 支持指数退避算法
     - 支持随机抖动
     - 支持服务级别的重试策略
     - 支持动态配置更新
     - 提供详细的监控指标
     - 与 Spring Cloud Gateway 完美集成
   - 配置示例：
     ```yaml
     retry:
       default-config:
         max-attempts: 3
         initial-interval: 1000
         max-interval: 10000
         multiplier: 2.0
         jitter: 0.1
         retryable-statuses: ['BAD_GATEWAY', 'SERVICE_UNAVAILABLE', 'GATEWAY_TIMEOUT']
         retryable-methods: ['GET', 'POST']
       services:
         auth-service:
           service-name: whiskerguard-auth-service
           enabled: true
           max-attempts: 3
           initial-interval: 1000
           max-interval: 5000
           multiplier: 2.0
           jitter: 0.1
           retryable-statuses: ['BAD_GATEWAY', 'SERVICE_UNAVAILABLE', 'GATEWAY_TIMEOUT']
           retryable-methods: ['GET', 'POST']
     ```
   - 监控指标：
     - retry.attempts：重试尝试次数
     - retry.failures：重试失败次数
     - retry.rate：重试速率（每分钟）
     - 所有指标都包含服务标签，支持按服务分析

3. **熔断机制**

   - 集成 Resilience4j
   - 配置熔断阈值
   - 实现自定义回退策略
   - 配置 Auth Service 熔断策略

   **熔断机制代码实现历史**

   - 创建 `CircuitBreakerConfiguration` 类，实现服务级别的熔断策略配置：

     - 默认配置：失败率阈值 50%，等待时间 60s，半开状态调用次数 10 次
     - 认证服务配置：失败率阈值 30%，等待时间 30s，半开状态调用次数 5 次
     - 用户服务配置：失败率阈值 40%，等待时间 45s，半开状态调用次数 8 次

   - 创建 `CircuitBreakerMetrics` 类，实现熔断器指标监控：

     - 注册熔断器状态指标（关闭/开启/半开）
     - 注册失败率和慢调用率指标
     - 注册总调用次数、成功调用次数、失败调用次数指标
     - 注册被拒绝调用次数指标
     - 支持动态添加新熔断器的指标注册

   - 创建 `CircuitBreakerFallbackController` 类，实现服务降级处理：

     - 全局服务降级处理
     - 租户服务降级处理
     - 用户服务降级处理
     - 统一的降级响应格式

   - 配置文件中添加熔断器相关配置：
     - 启用熔断器
     - 配置指标导出
     - 配置事件发布
     - 配置共享配置

### 第四阶段：运维增强（预计 2 周）

### 1. 动态路由配置

- 实现 Consul KV 存储
- 配置路由刷新机制
- 添加路由变更日志
- 配置 Auth Service 路由规则

#### 动态路由配置代码实现历史

##### 已实现代码

- 新增文件：`src/main/java/com/whiskerguard/gateway/config/ConsulRouteProperties.java`
  - 定义了 Consul 路由配置属性类
- 新增文件：`src/main/java/com/whiskerguard/gateway/config/DynamicRouteConfiguration.java`
  - 实现了 Redis 和 Consul KV 双存储机制
- 新增文件：`src/main/java/com/whiskerguard/gateway/web/rest/RouteResource.java`
  - 实现了路由管理 REST API
- 修改文件：`src/main/resources/config/application.yml`
  - 添加了 Consul 路由相关配置

##### 功能说明

- 支持 Redis 和 Consul KV 双存储机制
- 支持路由配置的动态刷新
- 提供完整的 REST API 接口
- 支持路由状态监控
- 支持路由变更审计日志

##### 主要类说明

- `ConsulRouteProperties`: Consul 路由配置属性类
- `DynamicRouteConfiguration`: 动态路由配置核心类
- `RouteResource`: 路由管理 REST API 控制器

##### 配置说明

```yaml
consul:
  route:
    enabled: true
    route-key: gateway/routes
    watch-enabled: true
    watch-delay: 1000
```

##### API 接口

- GET `/api/gateway/route-definitions`: 获取所有路由配置
- POST `/api/gateway/route-definitions`: 更新路由配置
- DELETE `/api/gateway/route-definitions/{id}`: 删除指定路由
- POST `/api/gateway/route-definitions/refresh`: 刷新路由配置
- GET `/api/gateway/route-definitions/status`: 获取路由状态
- GET `/api/gateway/route-definitions/source`: 获取路由存储源信息

##### 依赖说明

- spring-cloud-starter-consul-config
- spring-cloud-starter-consul-discovery
- consul-api
- spring-boot-starter-data-redis-reactive

##### 安全控制

- 所有路由管理 API 都需要 ADMIN 权限
- 支持路由变更审计日志
- 支持路由状态监控

##### 监控指标

- 路由总数
- 活跃路由数
- 路由状态
- 存储源状态

##### 后续优化方向

- 添加路由版本控制
- 支持路由回滚
- 增强路由监控指标
- 优化路由刷新机制
- 添加路由变更通知机制

2. **健康检查**

   - 实现实例健康检查
   - 配置健康检查策略
   - 实现实例自动下线
   - 监控 Auth Service 健康状态
   - **4.2健康检查实现历史**
     - 修改 `application.yml`，补充 Consul 健康检查相关配置：
     - `healthCheckPath: /management/health`
     - `healthCheckInterval: 10s`
     - `healthCheckTimeout: 5s`
     - `healthCheckCriticalTimeout: 30s`
     - `deregister: true`
     - 新增 `src/main/java/com/whiskerguard/gateway/health/AuthServiceHealthIndicator.java`
     - 实现自定义健康指示器，主动探测 Auth Service 的 `/management/health` 端点
     - 检查 Auth Service 是否可用，并将其健康状态纳入本服务的 `/management/health` 统一输出
     - 采用 Spring Boot Actuator 标准扩展点，自动集成到健康检查体系
     - 通过 Consul UI 可自动感知服务健康状态，Auth Service 不可用时会自动下线

3. **监控指标**
   - 完善 Prometheus 指标
   - 配置告警规则
   - 实现监控面板
   - 添加认证相关指标
   - **监控指标代码实现历史**
     - 新增 `src/main/java/com/whiskerguard/gateway/monitoring/AuthMetrics.java`
     - 采集登录成功（auth.login.success）和登录失败（auth.login.failure）自定义指标
     - 通过 Micrometer 自动暴露到 /management/prometheus，便于 Prometheus 采集
   - 修改 `src/main/java/com/whiskerguard/gateway/web/rest/AuthenticateController.java`
     - 注入 AuthMetrics，在登录成功/失败时分别埋点
     - 实现认证相关业务指标的自动采集
   - 这些指标可在 Prometheus、Grafana 中可视化和配置告警，完全符合 JHipster 架构与最佳实践

### 第五阶段：高级特性（预计 2 周）

1. **灰度发布**

   - 实现流量分发策略
   - 配置灰度规则
   - 添加灰度监控
   - 支持基于租户的灰度

2. **缓存机制**

   - 实现响应缓存
   - 配置缓存策略
   - 添加缓存监控
   - 缓存用户权限信息
   - **5.2 缓存机制 代码实现历史**
     - 修改 `src/main/resources/config/application.yml`，新增 spring.cache 配置，启用 Redis 缓存，设置全局过期时间、key 前缀等。
     - 新增 `src/main/java/com/whiskerguard/gateway/config/CacheConfiguration.java`，通过 `@EnableCaching` 启用 Spring Cache。
     - 新增 `src/main/java/com/whiskerguard/gateway/filter/ResponseCacheControlFilter.java`，对所有 GET 请求自动添加 `Cache-Control: max-age=60, public` 响应头，实现响应缓存。
     - 新增 `src/main/java/com/whiskerguard/gateway/service/UserService.java`，通过 `@Cacheable` 注解将用户权限信息缓存到 Redis，减少下游服务压力。
     - Micrometer/Actuator 自动采集 Redis 缓存相关指标（如命中率、命中数、evict数等），暴露在 `/management/prometheus`，可用于 Prometheus/Grafana 监控和告警。
     - 以上实现完全符合 JHipster 架构和 process.md 要求，便于后续扩展和维护。

3. **API 文档聚合**

   - 实现 OpenAPI 聚合
   - 配置 Swagger UI
   - 添加文档版本控制
   - 集成 Auth Service API 文档

   **API 文档聚合代码实现历史**

   - 修改 `src/main/resources/config/application.yml`，补充 springdoc 配置，确保 API 文档聚合和 Swagger UI 可用。
   - 新增 `src/main/java/com/whiskerguard/gateway/web/rest/OpenApiAggregationResource.java`，实现下游服务 OpenAPI 文档聚合，暴露 `/v3/api-docs-aggregate` 端点。
   - 通过 WebClient 并发拉取所有下游服务的 `/v3/api-docs`，聚合为统一 JSON 输出。
   - 访问 `/swagger-ui.html` 可查看聚合文档，前端可用聚合端点自定义聚合页面。
   - 以上实现完全符合 JHipster 架构和 process.md 要求，便于后续扩展和维护。

## 技术依赖

1. **核心依赖**

   - Spring Cloud Gateway
   - Spring Security
   - Spring Cloud Consul
   - Resilience4j
   - Redis
   - Spring OAuth2 Resource Server

2. **监控依赖**

   - Spring Boot Actuator
   - Prometheus
   - Zipkin/OpenTelemetry

3. **文档依赖**
   - SpringDoc OpenAPI
   - Swagger UI

## 注意事项

1. **安全性**

   - 所有配置变更需要经过安全审查
   - 敏感信息需要加密存储
   - 定期进行安全审计
   - 确保与 Auth Service 的安全通信

2. **性能**

   - 定期进行性能测试
   - 监控系统资源使用
   - 优化热点代码
   - 优化 Auth Service 调用

3. **可维护性**

   - 保持代码规范
   - 完善文档
   - 添加单元测试
   - 记录与 Auth Service 的集成细节

4. **可用性**
   - 实现优雅降级
   - 配置故障转移
   - 保持服务稳定性
   - 处理 Auth Service 不可用的情况

## 后续规划

1. **持续优化**

   - 根据监控数据优化性能
   - 根据用户反馈改进功能
   - 定期更新依赖版本
   - 优化与 Auth Service 的集成

2. **功能扩展**

   - 支持更多认证方式
   - 添加更多监控指标
   - 优化用户体验
   - 扩展与 Auth Service 的集成功能

3. **运维支持**
   - 完善运维文档
   - 提供运维工具
   - 建立运维规范
   - 提供 Auth Service 集成指南

WITH AI interaction rules:

现在我们来进行"第A阶段：运维增强"的"**AAAAA**"功能实现：

- 要符合 JHipster 项目的架构要求
- 要审查之前的代码是否已经有重复实现的，不要老是独立创建，能和之前代码融合的可以考虑
- 要结合 process.md 文档之前已经完成的功能来综合评估，该如何实现这块的功能。
